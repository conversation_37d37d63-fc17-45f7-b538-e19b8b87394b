package com.qudian.idle.bifrost.kernel;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁标识
 * <AUTHOR>
 * @date 2022/06/21
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLocker {

    /**
     * 锁名称
     * @return {@link String}
     */
    String name() default "";

    /**
     * 锁键值
     * @return {@link String}
     */
    String key() default "";

    /**
     * 过期时间
     * @return int
     */
    int expire() default 5000;

    TimeUnit unit() default TimeUnit.MILLISECONDS;
}
