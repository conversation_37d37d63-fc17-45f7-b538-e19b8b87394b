<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qudian.idle.bifrost</groupId>
        <artifactId>bifrost</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>bifrost-kernel</artifactId>
    <version>${bifrost.base.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.qudian.idle.bifrost</groupId>
            <artifactId>bifrost-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qudian.idle.bifrost</groupId>
            <artifactId>bifrost-common</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
    </dependencies>
</project>
