package com.qudian.idle.bifrost.api.vo.request.express;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class UpdateExpressReqVO  extends BaseRequestVO {


    /**
     * 客户订单号 nouse
     */
    private String orderNo;
    /**
     * 运单号 ，通过该字段修改对应快递单信息
     */
    private String waybillNo;
    /**
     * 要求上门取件开始时间, YYYY-MM-DD HH24:MM:SS， 示例： 2012-7-30 09:30:00 ，若该字段没有赋值，默认开始时间为当前时间
     */
    private String sendStartTm;
    /**
     * 托寄物信息
     */
    private List<CreateExpressReqVO.CargoDetail> cargoDetails;
    /**
     * 到件方信息
     */
    private CreateExpressReqVO.ContactInfo destContactInfo;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class  CargoDetail{
        private String name;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static  class  ContactInfo{
        /**
         * 联系人名称
         */
        private String contact;
        /**
         * 地址
         */
        private String address;
        /**
         * 电话
         */
        private String mobile;
        /**
         * 城市
         */
        private String city;
        /**
         * 省份
         */
        private String province;

    }



}
