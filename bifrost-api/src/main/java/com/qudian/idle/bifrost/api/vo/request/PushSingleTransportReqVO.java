package com.qudian.idle.bifrost.api.vo.request;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/5
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushSingleTransportReqVO extends BaseRequestVO {
    /**
     * 订单渠道，不传不校验
     */
    private String channel;
    /**
     * 选择的承运商，不传会使用兜底策略
     */
    private String carrier;
    @Valid
    private Shipment shipment;
    /**
     * 仓库所在州
     */
    private String warehouseLocationState;
    /**
     * 加一个来源
     * fhe
     * 电商
     */
    private String origin;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Shipment implements Serializable {
        /**
         * 发件人信息
         */
        private SenderInfo senderInfo;
        /**
         * 收件人信息
         */
        private RecipientInfo recipientInfo;
        /**
         * 退件信息
         */
        private ReturneeInfo  returneeInfo;
        /**
         * 物品信息
         */
        private List<ItemInfo> itemInfo;
        @NotBlank(message = "order is null")
        private String orderNumber;
        private String orderType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SenderInfo implements Serializable {
        /**
         * 发件人名字
         */
        private String sender;
        private String senderMobilePhone;
        private String senderAddress;
        /**
         * 发件人国家
         */
        private String senderCountry;
        private String senderProvince;
        private String senderCity;
        private String senderPostcode;
        /**
         * 市 （类似于城市概念）
         */
        private String county;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipientInfo implements Serializable {
        /**
         * 收件人名字
         */
        private String recipient;
        private String recipientMobilePhone;
        private String recipientAddress;
        private String receiptCountry;
        private String receiptProvince;
        private String receiptCity;
        private String receiptPostcode;
        private String county;

        private String email;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReturneeInfo implements Serializable {
        /**
         * 退件人名字
         */
        private String returnee;
        private String returneeMobilePhone;
        private String returneeAddress;
        private String returneeCountry;
        private String returneeProvince;
        private String returneeCity;
        private String returneePostcode;
        private String county;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemInfo implements Serializable {
        /**
         * 单位kg
         */
        private Double weight;
        /**
         * 单位cm
         */
        private Double width;
        /**
         * 单位cm
         */
        private Double length;
        /**
         * 单位cm
         */
        private Double height;
        private String itemDescription;
    }
}
