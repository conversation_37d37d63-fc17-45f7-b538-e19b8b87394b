package com.qudian.idle.bifrost.api.vo.request;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>文件名称:com.qudian.lme.driver.api.vo.EchoVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/25
 */
@Data
public class ValleyEchosReqVO extends BaseRequestVO {
    private static final long serialVersionUID = 3239370130333521183L;
    @NotNull(message = "validate.msg")
    private String heyRoar;
    private String origin;
}
