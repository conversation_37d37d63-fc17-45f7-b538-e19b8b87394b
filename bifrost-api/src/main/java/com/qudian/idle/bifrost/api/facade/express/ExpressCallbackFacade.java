package com.qudian.idle.bifrost.api.facade.express;


import com.qudian.idle.bifrost.api.vo.request.express.trace.SfExpressTraceCallbackReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.TraceCallbackRespVO;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.facade.express.ExpressCallbackFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
public interface ExpressCallbackFacade {
    TraceCallbackRespVO traceCallback(SfExpressTraceCallbackReqVO reqVO);
}
