package com.qudian.idle.bifrost.api.facade.express;

import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CancelExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchSingleRouteReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CancelExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.UpdateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.trace.SearchSingleRouteRespVO;
import com.qudian.idle.bifrost.api.vo.share.RawDataList;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
public interface ExpressFacade {

    /**
     * 下单
     * @param createExpressReqVO
     * @return
     */
    BaseResponseDTO<CreateExpressRespVO> create(CreateExpressReqVO createExpressReqVO);

    /**
     * 修改下单部分信息(仅支持修改接口中的字段，如果要修改其他字段，请取消后重新下单)
     * @param updateExpressReqVO
     * @return
     */
    BaseResponseDTO<UpdateExpressRespVO> update(UpdateExpressReqVO updateExpressReqVO);


    /**
     * 取消订单
     * @param cancelExpressReqVO
     * @return
     */
    BaseResponseDTO<CancelExpressRespVO> cancel(CancelExpressReqVO cancelExpressReqVO);


    /**
     * 批量打印面单
     * @param batchPrintReqVO
     * @return
     */
    BaseResponseDTO<BatchPrintRespVO> batchPrint(BatchPrintReqVO batchPrintReqVO);

    BaseResponseDTO<String> searchRoutes(SearchRoutesReqVO searchRoutesReqVO);
    BaseResponseDTO<RawDataList<SearchExpressRespVO>> searchExpress(SearchExpressReqVO searchExpressReqVO);

    /**
     * 查询物流路由
     *
     * @param searchSingleRouteReqVO
     * @return {@link BaseResponseDTO }<{@link SearchSingleRouteRespVO }>
     */
    BaseResponseDTO<SearchSingleRouteRespVO> querySingleRoute(SearchSingleRouteReqVO searchSingleRouteReqVO);



}
