package com.qudian.idle.bifrost.api.vo.response;

import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Deprecated
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushTransportResponseVO extends BaseResponseVO {
    private String senderReferences;
    private String forwarderType;
    private List<Item> items;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item implements Serializable {
        private String articleId;
        private String consignmentId;
        private String item_description;
        private String pdfUrl;
    }
}
