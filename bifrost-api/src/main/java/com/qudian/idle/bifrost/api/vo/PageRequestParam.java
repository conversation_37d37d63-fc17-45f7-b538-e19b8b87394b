package com.qudian.idle.bifrost.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * <p>文件名称:com.qudian.qpark.api.vo.page.PageRequestParam</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020-09-17 3:47 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageRequestParam extends BaseRequestVO {
    private static final long serialVersionUID = 4080892333276363102L;
    /**
     * 页大小
     */
    private int pageSize = 10;
    /**
     * 当前页
     */
    private int pageNumber = 1;

    public PageRequestParam(Long userId, int pageSize, int pageNumber) {
        super(userId, UUID.randomUUID().toString());
        this.pageSize = pageSize;
        this.pageNumber = pageNumber;
    }
}
