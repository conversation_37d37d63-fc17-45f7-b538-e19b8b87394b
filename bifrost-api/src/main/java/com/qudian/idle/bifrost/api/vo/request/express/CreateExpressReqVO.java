package com.qudian.idle.bifrost.api.vo.request.express;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CreateExpressReqVO extends BaseRequestVO {

    /**
     * 客户订单号
     */
    private String orderNo;

    /**
     * 快递对接订单号，调用方无需关注
     */
    private String expressOrderNo;

    /**
     * 要求上门取件开始时间, YYYY-MM-DD HH24:MM:SS， 示例： 2012-7-30 09:30:00 ，若该字段没有赋值，默认开始时间为当前时间
     */
    private String sendStartTm;
    /**
     * 创建人 Id
     */
    private String createdId;
    /**
     * 下单来源 ：source com.qudian.idle.bifrost.api.enums.ExpressSourceEnum
     */
    private String source;
    /**
     * 创建人名称
     */
    private String createdName;
    /**
     * 托寄物信息
     */
    private List<CargoDetail> cargoDetails;
    /**
     * 寄件方信息
     */
    private ContactInfo sendContactInfo;
    /**
     * 到件方信息
     */
    private ContactInfo destContactInfo;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class  CargoDetail{
        private String name;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static  class  ContactInfo{
        /**
         * 联系人名称
         */
        private String contact;
        /**
         * 地址
         */
        private String address;
        /**
         * 电话
         */
        private String mobile;
        /**
         * 城市
         */
        private String city;
        /**
         * 省份
         */
        private String province;

    }

}
