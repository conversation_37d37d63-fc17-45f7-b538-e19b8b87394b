package com.qudian.idle.bifrost.api.vo.response.express;

import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BatchPrintRespVO  extends BaseResponseVO {
    private List<BatchPrintResp> batchResponse;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BatchPrintResp{
        private  String waybillNo;
        private  String pdfObjectKey;
        private  String pdfUrl;
    }

}
