package com.qudian.idle.bifrost.api.vo.request;

import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/5
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleOrderTransportReqVO extends BaseRequestVO {

    private String forwarderType = ForwarderTypeEnum.EWE.getName();
    /**
     * 选择的承运商，不传会使用兜底策略
     */
    private String carrier;

    @NotBlank(message = "order is null")
    private String orderNumber;

    private String state;

    private String channel;
    // 来源
    private String origin;

    private String thirdOrderNumber;
}
