package com.qudian.idle.bifrost.api.vo.request;

import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ModifyTransportReqVO extends BaseRequestVO {

    @Deprecated
    private String forwarderType = ForwarderTypeEnum.EWE.getName();
    /**
     * 选择的承运商，不传会使用兜底策略
     */
    private String carrier;

    @NotBlank(message = "order is null")
    private String orderNumber;
    private String reason;
    private SenderInformation senderInformation;
    private RecipientInformation recipientInformation;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SenderInformation implements Serializable {
        private String sender;
        private String senderMobilePhone;
        private String senderAddress;
        private String senderCountry;
        private String senderProvince;
        private String senderCity;
        private String senderPostcode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipientInformation implements Serializable {
        private String to;
        private String recipientMobilePhone;
        private String recipientAddress;
        private String receiptCountry;
        private String receiptProvince;
        private String receiptCity;
        private String receiptPostcode;
    }
}
