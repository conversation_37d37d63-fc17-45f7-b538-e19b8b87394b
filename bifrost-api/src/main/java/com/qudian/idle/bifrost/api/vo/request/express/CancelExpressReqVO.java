package com.qudian.idle.bifrost.api.vo.request.express;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CancelExpressReqVO  extends BaseRequestVO {

    /**
     * 运单号，通过运单号取消
     */
    private String waybillNo;

    /**
     * 快递对接订单号 调用方无需关注
     */
    private String expressOrderNo;
}
