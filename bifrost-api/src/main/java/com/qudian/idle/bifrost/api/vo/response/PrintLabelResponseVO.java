package com.qudian.idle.bifrost.api.vo.response;

import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrintLabelResponseVO extends BaseResponseVO {

    private String objectKey;
    private String signedUrl;
    private String expiredAt;
    private String pdfUrl;
}
