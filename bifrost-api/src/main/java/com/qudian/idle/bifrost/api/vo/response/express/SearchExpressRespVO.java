package com.qudian.idle.bifrost.api.vo.response.express;

import com.qudian.idle.bifrost.api.enums.ExpressOrderStatusEnum;
import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Huang
 * @date 2025/9/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SearchExpressRespVO extends BaseResponseVO {
    private String orderNo;

    /**
     * 寄件方手机号
     */
    private String senderPhone;
    /**
     * 收件方手机号
     */
    private String receiverPhone;
    /**
     * 快递运单号
     */
    private String waybillNo;
    /**
     * 平台：1-顺丰
     */
    private String platform;
    /**
     * 下单详细信息
     */
    private String expressJson;
    /**
     * @see ExpressOrderStatusEnum
     * 订单状态：0-初始化 1-已下单 2-已取消 3-已揽收 4-运输中 5-已派送 6-已签收 7-异常
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createdId;
    private String createdName;
}