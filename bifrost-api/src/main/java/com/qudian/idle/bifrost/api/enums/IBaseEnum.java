package com.qudian.idle.bifrost.api.enums;

import java.util.Objects;

public interface IBaseEnum {
    /**
     * 用于显示的枚举名
     *
     * @return
     */
    String getDesc();

    /**
     * 存储到数据库的枚举值
     *
     * @return
     */
    Integer getCode();

    //按枚举的value获取枚举实例
    static <T extends IBaseEnum> T fromValue(Class<T> enumType, Integer value) {
        for (T object : enumType.getEnumConstants()) {
            if (Objects.equals(value, object.getCode())) {
                return object;
            }
        }
        throw new IllegalArgumentException("No enum value " + value + " of " + enumType.getCanonicalName());
    }
}
