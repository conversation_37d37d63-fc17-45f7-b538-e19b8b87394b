package com.qudian.idle.bifrost.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Title:  TransportStatusEnum.java
 * @Package com.qudian.idle.bifrost.api.enums
 * @Description:  $DESC
 * @Author: zhouqingyang
 * @Date:   2023/12/19 19:55
 * @Version V1.0
 */
@AllArgsConstructor
@Getter
public enum TransportStatusEnum implements IBaseEnum {

    CANCEL_SHIPMENT(0, "取消转运"),
    PUSH_UNCREATED_SHIPMENTS(1, "转运下单"),
    PRINT_LABEL(2, "打印面单"),
    DISPATCH_SHIPMENTS(3, "承运商转运"),
    SHIPMENT_SIGN(4, "转运签收");

    public final Integer code;
    public final String desc;

    public static TransportStatusEnum byCode(Integer code) {
        return Arrays.stream(TransportStatusEnum.values()).filter(f -> f.code.equals(code)).findFirst().orElseThrow(() -> new RuntimeException("未匹配查询类型"));
    }
}

