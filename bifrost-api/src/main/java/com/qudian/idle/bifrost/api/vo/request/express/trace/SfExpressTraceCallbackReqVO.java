package com.qudian.idle.bifrost.api.vo.request.express.trace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.vo.request.express.trace.SfExpressTraceCallbackReqVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 * @link  <a href="https://open.sf-express.com/Api/ApiDetails?level3=404&interName=%E8%B7%AF%E7%94%B1%E6%8E%A8%E9%80%81%E6%8E%A5%E5%8F%A3-RoutePushService">顺丰轨迹推送接口</a>
 */
@Data
@Accessors(chain = true)
public class SfExpressTraceCallbackReqVO implements Serializable {
    private List<SfExpressRouteDetailDTO> waybillRoute;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SfExpressRouteDetailDTO implements Serializable {
        private String mailno;
        private String acceptAddress;
        private String reasonName;
        private String orderid;
        private String acceptTime;
        private String remark;
        private String opCode;
        private String id;
        private String reasonCode;
    }
}
