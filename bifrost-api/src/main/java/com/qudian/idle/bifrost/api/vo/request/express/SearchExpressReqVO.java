package com.qudian.idle.bifrost.api.vo.request.express;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/9/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SearchExpressReqVO extends BaseRequestVO {
    /**
     * 订单号
     */
    private String orderNo;
    private String waybillNo;
    private String status;
    /**
     * 创建人 Id
     */
    private String createdId;
    /**
     * 下单来源 ：source com.qudian.idle.bifrost.api.enums.ExpressSourceEnum
     */
    private String source;
    /**
     * 寄件方手机
     */
    private String sendContactPhone;
    /**
     * 到件方手机
     */
    private String destContactPhone;
}
