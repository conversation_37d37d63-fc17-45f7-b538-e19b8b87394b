package com.qudian.idle.bifrost.api.vo.request.backdoor;

import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: yang<PERSON><PERSON>
 * @Date: 2023/11/27
 * @Version: 1.0.0
 **/
@Data
@Accessors(chain = true)
public class BackdoorOrderDeliveredReqVO extends BaseRequestVO {
    /**
     * 重发签收消息的order
     */
    private List<String> orders;
    /**
     * 是否强制改成签收状态
     */
    private Boolean force = Boolean.FALSE;
}
