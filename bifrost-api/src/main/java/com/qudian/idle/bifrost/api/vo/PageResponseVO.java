package com.qudian.idle.bifrost.api.vo;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>文件名称:com.qudian.lme.driver.api.vo.PageResponseVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/26
 */
@Data
@ToString(callSuper = true)
public class PageResponseVO extends BaseResponseVO{
    private static final long serialVersionUID = 7090170000356047884L;
    // 当前页码
    private Integer page;
    // 每页显示的记录数
    private Integer rowNum;
    // 总记录数
    private Integer records;
    // 返回数据
    private List<Object> rows = new ArrayList<>();

}
