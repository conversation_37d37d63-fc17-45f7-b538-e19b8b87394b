package com.qudian.idle.bifrost.api.vo.request;

import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Deprecated
@Data
public class OrderTransportReqVO extends BaseRequestVO {
    private String forwarderType = ForwarderTypeEnum.EWE.getName();
    @Size(min = 1, message = "orderNumbers not null")
    private List<String> orderNumbers;

    private String state;
    private String channel;
    private String origin;
}
