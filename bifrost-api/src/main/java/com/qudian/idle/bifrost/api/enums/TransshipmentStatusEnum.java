package com.qudian.idle.bifrost.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * {@inheritDoc} 转运状态枚举 http://wiki.qudian.com/pages/viewpage.action?pageId=72902963
 *
 * <AUTHOR>
 * @since 2023/8/10
 **/
@AllArgsConstructor
@Getter
public enum TransshipmentStatusEnum {
    /**
     * 转运待揽收
     */
    WAITING_PICKUP("waitingPickup"),
    /**
     * 转运配送中
     */
    IN_TRANSIT("inTransit"),
    /**
     * 转运到达待取
     */
    AWAITING_COLLECTION("awaitingCollection"),
    /**
     * 转运已签收
     */
    DELIVERED("delivered"),
    /**
     * 转运异常
     */
    ABNORMAL("abnormal"),
    /**
     * 未知
     */
    UNKNOWN("unknown"),
    ;

    private final String code;
}
