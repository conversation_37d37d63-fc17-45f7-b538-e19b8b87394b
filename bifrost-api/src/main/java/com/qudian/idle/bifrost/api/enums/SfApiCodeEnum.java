package com.qudian.idle.bifrost.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Huang
 * https://open.sf-express.com/Api?category=1&apiClassify=1
 * @date 2025/8/28
 */
@AllArgsConstructor
@Getter
public enum SfApiCodeEnum {
    CREATE_ORDER("CREATE_ORDER", "EXP_RECE_CREATE_ORDER", "下单"),
    CANCEL_ORDER("CANCEL_ORDER", "EXP_RECE_UPDATE_ORDER", "取消下单"),
    UPDATE_ORDER("UPDATE_ORDER", "EXP_RECE_UPDATE_ORDER", "修改快递单"),
    BATCH_PRINT_WAYBILLS("BATCH_PRINT_WAYBILLS", "COM_RECE_CLOUD_PRINT_WAYBILLS", "批量打印面单"),
    SEARCH_ROUTES("SEARCH_ROUTES", "EXP_RECE_SEARCH_ROUTES", "查询物流轨迹")
    ,;
    public String code;
    /**
     * 顺丰 接口 服务代码
     */
    private String serviceCode;
    private String desc;

}
