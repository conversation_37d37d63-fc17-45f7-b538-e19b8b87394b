package com.qudian.idle.bifrost.api.enums.trace;

import lombok.AllArgsConstructor;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.enums.trace.TraceSourceEnum</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@AllArgsConstructor
public enum TraceSourceEnum {
    PULL(1, "主动查询"),
    PUSH(2, "被动回调");

    public final Integer code;
    public final String desc;
}
