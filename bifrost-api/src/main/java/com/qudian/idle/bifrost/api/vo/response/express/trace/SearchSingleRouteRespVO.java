package com.qudian.idle.bifrost.api.vo.response.express.trace;

import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.vo.response.express.trace.SearchSingleRouteRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SearchSingleRouteRespVO extends BaseResponseVO {
    private List<SearchSingleRouteElem> waybillRoute;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SearchSingleRouteElem implements Serializable {
        private String waybillNo;
        private String event;   //轨迹事件，如“已揽件”
        private String eventTime; //轨迹触发的时间
        private String remark;
    }
}
