package com.qudian.idle.bifrost.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件名称:com.qudian.qpark.api.vo.page.PagingList</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">linqisong</a>
 * @version 1.0
 * @since 2020-09-17 3:47 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PagingList<T> implements Serializable {
    private static final long serialVersionUID = -5204034592643253845L;
    /**
     * 总数
     */
    private long total;
    /**
     * 每页的数量
     */
    private long pageSize;
    /**
     * 当前页
     */
    private long pageNum;
    /**
     * 是否还有下一页
     */
    private boolean hasMore;
    /**
     * 结果对象
     */
    private List<T> resultList;
}
