package com.qudian.idle.bifrost.api.facade.backdoor;

import com.qudian.idle.bifrost.api.vo.request.backdoor.BackdoorOrderDeliveredReqVO;
import com.qudian.java.components.common.dto.BaseResponseDTO;

/**
 * @Author: yang<PERSON>ye
 * @Date: 2023/11/27 15:59
 * @Version: 1.0.0
 **/
public interface BackdoorFacade {

    /**
     * 转运单签收mq处理
     * invoke com.qudian.idle.bifrost.api.facade.backdoor.BackdoorFacade:1.0.0.orderDelivered({"orders":["111"], "force":false})
     * @param reqVO
     * @return
     */
    BaseResponseDTO<Boolean> orderDelivered(BackdoorOrderDeliveredReqVO reqVO);

}
