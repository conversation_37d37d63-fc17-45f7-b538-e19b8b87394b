package com.qudian.idle.bifrost.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Huang
 * @date 2025/8/29
 */
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 快递订单状态枚举
 * 对应状态说明：0-初始化 1-已下单 2-已取消 3-已揽收 4-运输中 5-已派送 6-已签收 7-异常
 */
@Getter
@AllArgsConstructor
public enum ExpressOrderStatusEnum {

    /**
     * 初始化状态
     * 场景：订单刚创建，未发起任何物流操作（如未调用顺丰下单接口）
     */
    INIT(0, "初始化"),

    /**
     * 已取消状态
     * 场景：已调用取消接口（如顺丰EXP_RECE_UPDATE_ORDER），或下单后主动放弃物流
     */
    CANCELLED(10, "已取消"),

    /**
     * 已下单状态
     * 场景：已成功调用物流接口（如顺丰EXP_RECE_CREATE_ORDER），获取运单号但未揽收
     */
    ORDERED(20, "已下单"),

    /**
     * 已揽收状态
     * 场景：快递员已上门取件，物流系统更新为“揽收完成”（可通过物流轨迹接口获取）
     */
    PICKED_UP(30, "已揽收"),

    /**
     * 运输中状态
     * 场景：包裹在中转中心之间运输（如从北京中转到上海中转），未到达派送站点
     */
    IN_TRANSIT(40, "运输中"),

    /**
     * 已派送状态
     * 场景：包裹到达派送站点，快递员正在派送途中（未签收）
     */
    DELIVERING(50, "已派送"),

    /**
     * 已签收状态
     * 场景：收件人已确认签收（含本人签收、他人代收）
     */
    SIGNED(60, "已签收"),

    /**
     * 异常状态
     * 场景：物流环节出现问题（如地址错误、包裹丢失、拒签等），需人工介入处理
     */
    EXCEPTION(70, "异常"),

    /**
     * 无法穷举出的状态
     */
    UNKNOWN(80, "未知状态")
    ,;

    /**
     * 状态编码（与业务系统/物流接口返回码对齐）
     */
    private final Integer code;

    /**
     * 状态描述（用于日志打印、前端展示）
     */
    private final String desc;

    /**
     * 根据状态编码获取枚举实例（避免空指针，推荐使用）
     * @param code 状态编码（如0、1、2...）
     * @return 对应的枚举实例，若编码不存在则返回null
     */
    public static ExpressOrderStatusEnum getByCode(Integer code) {
        // 遍历枚举所有值，匹配编码
        for (ExpressOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        // 编码无效时返回null（也可抛出自定义异常，根据业务需求调整）
        return ExpressOrderStatusEnum.UNKNOWN;
    }

    /**
     * 校验状态编码是否有效（用于参数校验）
     * @param code 状态编码
     * @return true=有效编码，false=无效编码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
