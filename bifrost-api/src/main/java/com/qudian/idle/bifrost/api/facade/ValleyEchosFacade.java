package com.qudian.idle.bifrost.api.facade;

import com.qudian.idle.bifrost.api.vo.request.ValleyEchosReqVO;
import com.qudian.java.components.common.dto.BaseResponseDTO;

import javax.validation.Valid;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.facade.ValleyEchosFacade</p>
 * <p>文件描述: 连通性测试</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/7/25
 */
public interface ValleyEchosFacade {
    BaseResponseDTO<ValleyEchosReqVO> echo(@Valid ValleyEchosReqVO reqVO);
}
