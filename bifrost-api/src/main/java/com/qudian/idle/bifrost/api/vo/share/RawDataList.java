package com.qudian.idle.bifrost.api.vo.share;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RawDataList<T> implements Serializable {
    private static final long serialVersionUID = -5204034592643253845L;
    /**
     * 结果对象
     */
    private List<T> resultList;

    public static <T> RawDataList getRawDataList(List<T> page) {
        RawDataList rawDataList = new RawDataList();
        rawDataList.setResultList(page);
        return rawDataList;
    }
}
