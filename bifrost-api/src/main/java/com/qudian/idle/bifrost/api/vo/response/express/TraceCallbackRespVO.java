package com.qudian.idle.bifrost.api.vo.response.express;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.idle.bifrost.api.vo.response.express.TraceCallbackRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TraceCallbackRespVO implements Serializable {
    private String return_code;
    private String return_msg;

    public static TraceCallbackRespVO success() {
        return new TraceCallbackRespVO("0000", "成功");
    }
}
