package com.qudian.idle.bifrost.common.enums;

import com.google.common.collect.ImmutableList;
import com.qudian.idle.bifrost.common.constant.AusTransportConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum TransportStatusEnum {
    CANCEL_SHIPMENT(0, "取消转运"),
    PUSH_UNCREATED_SHIPMENTS(1, "转运下单"),
    PRINT_LABEL(2, "打印面单"),
    DISPATCH_SHIPMENTS(3, "澳邮转运"),
    SHIPMENT_SIGN(4, "转运签收"),

    ;

    public static final List<Integer> TRACK_SYNC_STATUS_LIST = ImmutableList.of(PUSH_UNCREATED_SHIPMENTS.getCode(), PRINT_LABEL.getCode(), DISPATCH_SHIPMENTS.getCode());
    private Integer code;
    private String msg;

    public static String createTransportStatusRedisKey(String transportType, String orderNumber) {
        return String.format(AusTransportConstant.TRANSPORT_REDIS_PREFIX, transportType, orderNumber);
    }
}
