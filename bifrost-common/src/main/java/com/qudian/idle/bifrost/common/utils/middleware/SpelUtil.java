package com.qudian.idle.bifrost.common.utils.middleware;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/21
 **/
public class SpelUtil {

    private SpelUtil() {
        throw new UnsupportedOperationException();
    }

    /**
     * 支持 #p0 参数索引的表达式解析
     *
     * @param rootObject 根对象,method 所在的对象
     * @param spel       表达式
     * @param method     ，目标方法
     * @param args       方法入参
     * @return 解析后的字符串
     */
    public static <T> T parse(Object rootObject, String spel, Method method, Object[] args, Class<T> clazz) {
        if (StringUtils.isBlank(spel)) {
            return null;
        }
        //获取被拦截方法参数名列表(使用Spring支持类库)
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paraNameArr = u.getParameterNames(method);
        if (ArrayUtils.isEmpty(paraNameArr)) {
            return (T) spel;
        }
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new MethodBasedEvaluationContext(rootObject, method, args, u);
        //把方法参数放入SPEL上下文中
        int len = paraNameArr.length;
        for (int i = 0; i < len; i++) {
            context.setVariable(paraNameArr[i], args[i]);
        }
        return parser.parseExpression(spel).getValue(context, clazz);
    }

    /**
     * 使用SPEL解析对象
     *
     * @param res   res
     * @param spel
     * @param clazz clazz
     * @return {@link T}
     */
    public static <T> T parse(Object res, String spel, Class<T> clazz) {
        ExpressionParser parser = new SpelExpressionParser();
        return parser.parseExpression(spel).getValue(res, clazz);
    }
}
