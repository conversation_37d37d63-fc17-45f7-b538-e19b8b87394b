package com.qudian.idle.bifrost.common.limiter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.exceptions.JedisException;

import javax.annotation.Resource;

/**
 * {@inheritDoc} redis限流器
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@Slf4j
@Component
public class RedisLimiter {

    @Resource
    private JedisPool jedisPool;

    /**
     * 【redis限流器】请求频次是否超出限制
     *
     * @param key              限流key
     * @param timeRangeSeconds 时间范围
     * @param limitNum         在限制时间内可以请求的次数阈值
     * @return 是否访问超限
     */
    public boolean isOverLimit(String key, Integer timeRangeSeconds, Integer limitNum) {
        StringBuffer script = new StringBuffer();
        //  LUA脚本--如果超限，返回1；否则返回0
        //  set的返回值说明："OK"-key不存在，设置成功；null-key已存在
        script.append("local ok = redis.call('set', KEYS[1], 1, 'NX', 'EX', tonumber(ARGV[1])) \n")
            .append("if (ok) then  \n")
            .append("return 0 \n")
            .append("end \n")
            .append("local reqNum = redis.call('incr',KEYS[1])  \n")
            .append("if reqNum> tonumber(ARGV[2]) then  \n")
            .append("return 1  \n")
            .append("end  \n")
            .append("return 0  \n");
        Jedis jedis = null;
        long result;
        try {
            jedis = jedisPool.getResource();
            //  KEYS[1]:key ARGV[1]=timeRangeSeconds ARGV[2]=limitNum
            result = (long) jedis.eval(script.toString(), 1, key, timeRangeSeconds.toString(), limitNum.toString());
            //  执行lua脚本返回1，表示访问超限
            if (result == 1) {
                log.info("key:{} rate over limit, cur number:{}", key, jedis.get(key));
                return true;
            }
        } catch (JedisException e) {
            log.error("JedisException=", e);
            return true;
        } catch (Exception e) {
            log.error("jedis限流器异常", e);
            return true;
        } finally {
            jedis.close();
        }
        return false;
    }
}
