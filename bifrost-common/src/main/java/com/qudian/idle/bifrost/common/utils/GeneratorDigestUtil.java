package com.qudian.idle.bifrost.common.utils;

import com.qudian.idle.bifrost.common.constant.AusTransportConstant;
import org.apache.tomcat.util.codec.binary.Base64;

import java.security.MessageDigest;

public class GeneratorDigestUtil {
    public static String generatorDigest(String username, String password, String msgType, Integer shipments) {
        StringBuilder sb = new StringBuilder(username);
        if (shipments != null) {
            sb.append(shipments);
        }
        sb.append(msgType).append(password);
        String sign;

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sb.toString().getBytes(AusTransportConstant.CHARSET));
            sign = new String(Base64.encodeBase64(md.digest()), AusTransportConstant.CHARSET);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sign;
    }

    public static String generatorDigest(String username, String password, String msgType) {
        return generatorDigest(username, password, msgType, null);
    }

    public static String generatorDispatchShipmentsDigest(String username, String password, Integer shipments) {
        return generatorDigest(username, password, AusTransportConstant.DISPATCH_SHIPMENTS, shipments);
    }

    public static String generatorPushUnCreatedShipmentsDigest(String username, String password, Integer shipments) {
        return generatorDigest(username, password, AusTransportConstant.PUSH_UNCREATED_SHIPMENTS, shipments);
    }

    public static String generatorCancelShipmentDigest(String username, String password) {
        return generatorDigest(username, password, AusTransportConstant.CANCEL_SHIPMENT);
    }

    public static String generatorModifyShipmentDigest(String username, String password) {
        return generatorDigest(username, password, AusTransportConstant.MODIFY_SHIPMENT);
    }
}
