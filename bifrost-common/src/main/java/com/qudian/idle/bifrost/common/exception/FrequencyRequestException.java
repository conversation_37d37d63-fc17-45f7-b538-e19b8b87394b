package com.qudian.idle.bifrost.common.exception;

import com.qudian.java.components.common.exception.BaseException;
import com.qudian.idle.bifrost.common.enums.ExceptionEnum;


/**
 * 频繁请求异常
 * <AUTHOR>
 * @date 2022/11/07
 */
public class FrequencyRequestException extends BaseException {
    private static final long serialVersionUID = 8107625492656280955L;
    private static final ExceptionEnum code = ExceptionEnum.REPEAT;

    public FrequencyRequestException(String message) {
        super(code.getCode(), message, true);
    }

    public FrequencyRequestException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getCode(), exceptionEnum.getMsg(), true);
    }

    public FrequencyRequestException(ExceptionEnum exceptionEnum, String message) {
        super(code.getCode(), message, true);
    }
}
