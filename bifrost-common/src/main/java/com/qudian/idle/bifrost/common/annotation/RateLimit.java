package com.qudian.idle.bifrost.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * {@inheritDoc} redis限流器
 *
 * <AUTHOR>
 * @since 2023/7/31
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RateLimit {

    /**
     * 限流Key
     */
    String name() default "";

    /**
     * 限流url，如果不配置则按照方法级别限流
     */
    String url() default "";
}
