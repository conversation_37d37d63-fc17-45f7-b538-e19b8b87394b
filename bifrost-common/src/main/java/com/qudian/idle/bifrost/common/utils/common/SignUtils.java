package com.qudian.idle.bifrost.common.utils.common;

import cn.hutool.crypto.digest.HmacAlgorithm;
import com.qudian.idle.bifrost.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Slf4j
public class SignUtils {
    public static String createWiseWaySign(String secret, String requestUrl, String requestType,String time) {
        try {
            String s = requestType + "\n" + time + "\n" + requestUrl;
            return encrypt(s, secret);
        } catch (Exception e) {
            log.error("生成签名失败", e);
        }
        throw new BizException("生成签名失败，请及时排查原因");
    }

    public static String encrypt(String data, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] result = null;
        SecretKeySpec signinKey = new SecretKeySpec(secret.getBytes(), HmacAlgorithm.HmacSHA1.getValue());
        //生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(HmacAlgorithm.HmacSHA1.getValue());
        //用给定密钥初始化 Mac 对象
        mac.init(signinKey);
        //完成 Mac 操作
        byte[] rawHmac = mac.doFinal(data.getBytes());
        result = Base64.getEncoder().encode(rawHmac);

        return new String(result);

    }
}
