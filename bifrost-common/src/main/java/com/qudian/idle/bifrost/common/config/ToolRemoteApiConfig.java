package com.qudian.idle.bifrost.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <p>文件名称:com.qudian.lme.driver.common.config.ToolRemoteApiConfig</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/10
 */
@Data
@Configuration
@ConfigurationProperties("lme.tool")
@RefreshScope
public class ToolRemoteApiConfig {
    private String host;

    //上传文件
    private String uploadFileNotify = "/v1/storage/upload";
    private String signedUrl = "/v1/storage/signedurl";
    private String regionUrl = "/v1/region/query";
}
