package com.qudian.idle.bifrost.common.decorator;

import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.core.task.TaskDecorator;

/**
 * sw装饰器，用以解决traceId跨线程丢失
 * <AUTHOR>
 * @version 1.0
 * @since 2022/5/29
 **/
public class SkywalkingPoolTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        return RunnableWrapper.of(runnable);
    }
}
