package com.qudian.idle.bifrost.common.utils;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.qudian.idle.bifrost.common.annotation.RateLimit;
import com.qudian.idle.bifrost.common.dto.HttpRespDTO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.log.annotion.RequestLog;
import com.qudian.idle.bifrost.common.log.annotion.TokenClearWhenError;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;

import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.rmi.ServerException;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpClient {
    private final OkHttpClient okHttpClient;

    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public static final MediaType JAVASCRIPT = MediaType.parse("application/javascript; charset=utf-8");
    public static final MediaType TEXT_XML = MediaType.parse("application/xml; charset=utf-8");
    public static final MediaType REAL_TEXT_XML = MediaType.parse("text/xml; charset=UTF-8");
    public static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded");

    public HttpClient(long connectionTimeout, long readTimeout) {
        this.okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)
            .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
            .build();
    }

    public OkHttpClient getOkHttpClient(){
        return this.okHttpClient;
    }


    public byte[] download(String url, String token) throws IOException {
        log.info("开始下载文件, url: {}", url);
        // 创建临时文件
        String fileName = "sf_download_" + UUID.randomUUID().toString() + ".pdf";
//        File tempFile = new File(tempDir, fileName);

        // 构建请求，添加token
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-Auth-token", token)
                .get()
                .build();

        // 执行请求并获取响应流（直接返回InputStream，不落地到磁盘）
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("下载文件失败，HTTP状态码: " + response.code() + ", URL: " + url);
            }
            // 获取输入流（此时 Response 未关闭，流可用）
            try (InputStream inputStream = response.body().byteStream()) {
                if (inputStream == null) {
                    throw new IOException("文件内容为空，URL: " + url);
                }

                return IoUtil.readBytes(inputStream);
            }
        }
    }

    private String getResultStr(Response response, String url) throws IOException {
        String result = null;
        if (!response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                result = responseBody.string();
            }
            log.error("POST {},code={},res={}", url, response.code(), result);
            String message = String.format("http post error, code=%s, message=%s, url=%s",
                response.code(), response.message(), response.request().url());
            throw new ServerException(message);
        }
        assert response.body() != null;
        return response.body().string();
    }

    private String getResultStrForCtt(Response response, String url) throws IOException {
        ResponseBody responseBody = response.body();
        if (responseBody == null) {
            return null;
        }
        assert response.body() != null;
        String res = response.body().string();
        log.info("{},code={},res={}", url, response.code(),res);
        return res;
    }

    private String specialGetWiseWayResultStr(Response response, String url) throws IOException {
        String result = null;
        // 异常
        if (!response.isSuccessful()) {
            if (response.body() != null) {
                result = response.body().string();
            }
            log.error("POST {},code={},res={}", url, response.code(), result == null ? response.message() : result);
        }
        // 正常
        if (result == null) {
            if (response.body() == null) {
                String message = String.format("http post error, code=%s, message=%s, url=%s",
                    response.code(), response.message(), response.request().url());
                throw new ServerException(message);
            } else {
                result = response.body().string();
                log.info("result = {}", result);
            }
        }
        return result;
    }

    private byte[] getResultByte(Response response, String url) throws IOException {
        String result = null;
        if (!response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                result = responseBody.string();
            }
            log.error("get {},code={},res={}", url, response.code(), result);
            String message = String.format("http post error, code=%s, message=%s, url=%s",
                response.code(), response.message(), response.request().url());
            throw new ServerException(message);
        }
        assert response.body() != null;
        return response.body().bytes();
    }

    /**
     * get请求，同步方式，获取网络数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @return
     */
    public String getData(String url, Map<String, String> params) {
        // 1 构造Request
        HttpUrl.Builder httpBuilder = HttpUrl.parse(url).newBuilder();
        if (params != null) {
            for (Map.Entry<String, String> param : params.entrySet()) {
                httpBuilder.addQueryParameter(param.getKey(), param.getValue());
            }
        }
        Request request = new Request.Builder().get().url(httpBuilder.build()).build();
        // 2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 3 执行Call，得到response
        String result = null;
        try {
            Response response = call.execute();
            result = getResultStr(response, url);

        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * get请求，同步方式，获取网络数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @return
     */
    public String getData(String url) {
        // 1 构造Request
        Request.Builder builder = new Request.Builder();
        Request request = builder.get().url(url).build();
        // 2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 3 执行Call，得到response
        String result = null;
        try {
            Response response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public byte[] getByteData(String url) {
        // 1 构造Request
        Request.Builder builder = new Request.Builder();
        Request request = builder.get().url(url).build();
        // 2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 3 执行Call，得到response
        byte[] data = null;
        try {
            Response response = call.execute();
            data = getResultByte(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return data;
    }

    /**
     * post请求，同步方式，提交数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @param bodyParams
     * @return
     */
    public String postData(String url, Map<String, String> bodyParams) {
        // 1构造RequestBody
        RequestBody body = setRequestBody(bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        Response response = null;
        String result = null;
        try {
            response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public HttpRespDTO getRequestForCttWithLog(String url, Object body, Map<String, String> headerMap) {
        // 1构造RequestBody
        String fullUrl = processUrl(url, body);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        String result = null;
        Integer code = null;
        try {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
            Request request = requestBuilder.get().url(fullUrl).build();
            // 3 将Request封装为Call
            Call call = okHttpClient.newCall(request);
            Response response = call.execute();
            code = response.code();
            result = getResultStrForCtt(response, url);
        } catch (IOException e) {
            log.error("编码请求参数失败", e);
        }
        // 4 执行Call，得到response
        return new HttpRespDTO()
            .setResp(result)
            .setCode(code);

    }

    public String getRequest(String url, Object body, Map<String, String> headerMap) {
        // 1构造RequestBody
        String fullUrl = processUrl(url, body);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        String result = null;
        try {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
            Request request = requestBuilder.get().url(fullUrl).build();
            // 3 将Request封装为Call
            Call call = okHttpClient.newCall(request);
            Response response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            log.error("编码请求参数失败", e);
        }
        // 4 执行Call，得到response
        return result;
    }

    private static String processUrl(String url, Object body) {
        String queryString = beanToQueryString(body);
        String fullUrl;
        if (StringUtils.isBlank(queryString)) {
            fullUrl = url;
        } else {
            fullUrl = url + "?" + queryString;
        }
        return fullUrl;
    }

    /**
     * post请求，同步方式，提交数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @param bodyParams
     * @return
     */
    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postJsonData(String url, String bodyParams, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return postParamString(url, bodyParams, JSON, headerMap);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    @TokenClearWhenError(tokenKey = "ctt_token")
    public HttpRespDTO postJsonDataForCtt(String url, String bodyParams, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return postForCTT(url, bodyParams, JSON, headerMap);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postForm(String url, String bodyParams, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return postParamString(url, bodyParams, FORM, headerMap);
    }

    public String postJsonDataNoLog(String url, String bodyParams, Map<String, String> headerMap) {
        return postParamString(url, bodyParams, JSON, headerMap);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String deleteJsonData(String url, String bodyParams, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return deleteParamString(url, bodyParams, JSON, headerMap);
    }

    @RequestLog(requestUrl = "#url", requestBody = "T(com.alibaba.fastjson.JSON).toJSONString(#object)", bizParam = "#bizParam")
    public String getRequestWithLog(String url, Object object, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return getRequest(url, object, headerMap);
    }

    private String postJavascriptData(String url, String bodyParams) {
        return postParamString(url, bodyParams, JAVASCRIPT);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postXmlData(String url, String bodyParams, BizRequestLogParam bizParam) {
        return postParamString(url, bodyParams, TEXT_XML);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postSoapV2(String url, String bodyParams, BizRequestLogParam bizParam) {
        return postSoap(url, bodyParams, REAL_TEXT_XML);
    }

    @RequestLog(requestUrl = "#url", requestBody = "T(com.alibaba.fastjson.JSON).toJSONString(#body)", bizParam = "#bizParam")
    @TokenClearWhenError(tokenKey = "ctt_token")
    public HttpRespDTO getRequestForCttWithLog(String url, Object body, Map<String, String> headerMap, BizRequestLogParam bizParam) {
        return getRequestForCttWithLog(url, body, headerMap);
    }

    public String postParamString(String url, String bodyParams, MediaType mediaType) {
        // 1构造RequestBody
        RequestBody body = RequestBody.create(mediaType, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        String result = null;
        log.info("http request info, url: {}, params: {}", url, bodyParams);
        try {
            Response response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            log.error("http request error, url: {}, params: {}", url, bodyParams);
        }
        log.info("http response info, url: {}, response: {}", url, result);
        return result;
    }

    public String postSoap(String url, String bodyParams, MediaType mediaType) {
        // 1构造RequestBody
        RequestBody body = RequestBody.create(mediaType, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        String result = null;
        log.info("http request info, url: {}, params: {}", url, bodyParams);
        try {
            Response response = call.execute();
            if (Objects.isNull(response) || Objects.isNull(response.body())) {
                log.error("响应为空");
            } else {
                return parserResponseBody(response.body().string());
            }

        } catch (IOException e) {
            log.error("http request error, url: {}, params: {}", url, bodyParams);
        }
        log.info("http response info, url: {}, response: {}", url, result);
        return result;
    }

    public String postParamString(String url, String bodyParams, MediaType mediaType, Map<String, String> headerMap) {
        // 1构造RequestBody
        RequestBody body = RequestBody.create(mediaType, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();

        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        String result = null;
        log.info("http request info, url: {}, params: {}", url, bodyParams);
        try {
            Response response = call.execute();
            result = specialGetWiseWayResultStr(response, url);
        } catch (IOException e) {
            log.error("http request error, url: {}, params: {}", url, bodyParams);
        }
        log.info("http response info, url: {}, response: {}", url, result);
        return result;
    }

    public HttpRespDTO postForCTT(String url, String bodyParams, MediaType mediaType, Map<String, String> headerMap) {
        // 1构造RequestBody
        RequestBody body = RequestBody.create(mediaType, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();

        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        String result = null;
        Integer code = null;
        log.info("http request info, url: {}, params: {}", url, bodyParams);
        try {
            Response response = call.execute();
            code = response.code();
            result = getResultStrForCtt(response, url);
        } catch (IOException e) {
            log.error("编码请求参数失败", e);
        }
        // 4 执行Call，得到response
        return new HttpRespDTO()
            .setResp(result)
            .setCode(code);
    }

    public String deleteParamString(String url, String bodyParams, MediaType mediaType, Map<String, String> headerMap) {
// 1构造RequestBody
        RequestBody body = RequestBody.create(mediaType, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();

        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        Request request = requestBuilder.delete(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        String result = null;
        log.info("http request info, url: {}, params: {}", url, bodyParams);
        try {
            Response response = call.execute();
            result = specialGetWiseWayResultStr(response, url);
        } catch (IOException e) {
            log.error("http request error, url: {}, params: {}", url, bodyParams);
        }
        log.info("http response info, url: {}, response: {}", url, result);
        return result;
    }

    public String getParamStringWithHeader(String url, Headers headers) {
        // 1 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.get().url(url).headers(headers).build();
        // 2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 3 执行Call，得到response
        String result = null;
        log.info("http request info, url: {}", url);
        try {
            Response response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            log.error("http request error, url: {}", url);
        }
        log.info("http response info, url: {}, response: {}", url, result);
        return result;
    }

    /**
     * post请求，同步方式，提交数据 content-type:javascript
     *
     * @return response
     */
    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public Response postJavascriptDataReturnResponse(String url, String bodyParams, BizRequestLogParam bizParam) throws IOException {
        // 1 构造RequestBody
        RequestBody body = RequestBody.create(JAVASCRIPT, bodyParams);
        // 2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        // 3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        // 4 执行Call，得到response
        return call.execute();
    }

    /**
     * post请求，同步方式，提交数据 content-type:javascript
     *
     * @return response body
     */
    @RateLimit(name = "#bizParam.forwarderType", url = "#url")
    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postJavascriptData(String url, String bodyParams, BizRequestLogParam bizParam) {
        return postJavascriptData(url, bodyParams);
    }

    @RequestLog(requestUrl = "#url", requestBody = "#bodyParams", bizParam = "#bizParam")
    public String postJavascriptDataNoLimit(String url, String bodyParams, BizRequestLogParam bizParam) {
        return postJavascriptData(url, bodyParams);
    }

    @RequestLog(requestUrl = "#url", bizParam = "#bizParam")
    public String getDataWithHeader(String url, Headers headers, BizRequestLogParam bizParam) {
        return getParamStringWithHeader(url, headers);
    }

    /**
     * post的请求参数，构造RequestBody
     *
     * @param bodyParams
     * @return
     */
    private RequestBody setRequestBody(Map<String, String> bodyParams) {
        RequestBody body;
        FormBody.Builder formEncodingBuilder = new FormBody.Builder();
        if (bodyParams != null) {
            Iterator<String> iterator = bodyParams.keySet().iterator();
            String key;
            while (iterator.hasNext()) {
                key = iterator.next();
                formEncodingBuilder.add(key, bodyParams.get(key));
            }
        }
        body = formEncodingBuilder.build();
        return body;

    }

    @RequestLog(requestUrl = "#upsRemoteUrl", requestBody = "#params", bizParam = "#bizParam")
    public String curlToUps(String upsRemoteUrl, String apiMethod, String params, BizRequestLogParam bizParam) {
        log.info("curlToUps params: {}", params);

        HttpUrl.Builder uriBuilder = Objects.requireNonNull(HttpUrl.parse(upsRemoteUrl)).newBuilder();
        uriBuilder.addQueryParameter("API", apiMethod);
        uriBuilder.addQueryParameter("XML", params);
        Request request = new Request.Builder()
            .url(uriBuilder.build())
            .get()
            .build();
        log.info("curlToUps request: {}", request);
        try {
            Response response = okHttpClient.newCall(request).execute();
            String responseBody = response.body().string();
            log.info("curlToUps responseBody :{}", responseBody);
            return responseBody;
        } catch (Exception e) {
            log.error("curlToUps error", e);
        }
        return null;
    }

    public static String parserResponseBody(String result) {
        try {
            log.info("响应为:{}", result);
            MessageFactory factory = MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL);

            SOAPMessage message = factory.createMessage(null, new ByteArrayInputStream(result.getBytes()));

            // 获取SOAP消息的部分
            SOAPPart soapPart = message.getSOAPPart();

            // 获取SOAP消息的Envelope
            SOAPEnvelope envelope = soapPart.getEnvelope();
            // 获取SOAP消息的Body
            SOAPBody body = envelope.getBody();

            // 获取SOAP消息的Body
            // 遍历Body中的子元素
            SOAPElement responseElement = null;
            for (Iterator it = body.getChildElements(); it.hasNext(); ) {
                Object child = it.next();
                if (child instanceof SOAPElement) {
                    responseElement = (SOAPElement) child;
                    break;
                }
            }
            // 将SOAPElement转换为字符串
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(responseElement), new StreamResult(writer));

            // 获取OutputStream中的内容
            return writer.toString();
        } catch (SOAPException | IOException | TransformerException e) {
            log.error("解析响应失败", e);
        }
        throw new BizException("从gls解析响应失败");
    }

    private static String beanToQueryString(Object bean) {
        if (Objects.isNull(bean)) {
            return null;
        }
        try {
            StringBuilder queryStringBuilder = new StringBuilder();
            for (Field field : bean.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                JSONField jsonField = field.getAnnotation(JSONField.class);
                String fieldName = jsonField != null ? jsonField.name() : field.getName();
                Object value = field.get(bean);
                if (value != null) {
                    if (queryStringBuilder.length() > 0) {
                        queryStringBuilder.append("&");
                    }
                    queryStringBuilder.append(fieldName).append("=").append(value);
                }
            }
            return queryStringBuilder.toString();
        } catch (Exception e) {
            log.error("构建get请求参数失败", e);
        }
        throw new BizException("构建get请求参数失败");
    }

}
