package com.qudian.idle.bifrost.common.exception;

import com.qudian.java.components.common.exception.BaseException;
import com.qudian.idle.bifrost.common.enums.ExceptionEnum;


/**
 * 业务异常
 * <AUTHOR>
 * @date 2022/11/07
 */
public class BizException extends BaseException {

    private static final ExceptionEnum code = ExceptionEnum.SYSTEM_ERR;

    public BizException(String message) {
        super(code.getCode(), message, true);
    }

    public BizException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getCode(), exceptionEnum.getMsg(), true);
    }

    public BizException(ExceptionEnum exceptionEnum, String message) {
        super(code.getCode(), message, true);
    }
    public BizException(Integer code, String message) {
        super(code, message, true);
    }
}
