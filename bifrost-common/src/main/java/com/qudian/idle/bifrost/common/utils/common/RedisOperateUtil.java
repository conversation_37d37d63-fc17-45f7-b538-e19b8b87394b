package com.qudian.idle.bifrost.common.utils.common;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.jedis.JedisConnection;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Date 2019-10-28 15:51
 **/
@Slf4j
@Component
public class RedisOperateUtil {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private JedisConnectionFactory jedisConnectionFactory;

    private static final Long RELEASE_SUCCESS = 1L;

    /**
     * @param key
     * @param value
     * @param exptime (second)
     * @return
     */
    public boolean setIfAbsent(String key, Serializable value, long exptime) {
        return (Boolean) redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            RedisSerializer valueSerializer = redisTemplate.getValueSerializer();
            RedisSerializer keySerializer = redisTemplate.getKeySerializer();
            // 保证操作原子性
            Object obj = connection.execute("set", keySerializer.serialize(key),
                    valueSerializer.serialize(value),
                    "NX".getBytes(StandardCharsets.UTF_8),
                    "EX".getBytes(StandardCharsets.UTF_8),
                    String.valueOf(exptime).getBytes(StandardCharsets.UTF_8));
            return obj != null;
        });
    }

    public void delete(String key) {
        redisTemplate.delete(key);
    }

    public long incr(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    public long decr(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    /**
     * 匹配键值（使用scan替代keys，防止阻塞）
     * @param key
     * @return
     */
   public Set<String> listKeys(String key) {
       JedisTool jedisTool = null;
       try{
           jedisTool = new JedisTool();
           String cursor = ScanParams.SCAN_POINTER_START;
           Set<String> result = Sets.newHashSet();

           ScanParams scanParams = new ScanParams();
           scanParams.match(key);
           scanParams.count(1000);
           do {
               ScanResult<String> scanResult = jedisTool.getJedis().scan(cursor, scanParams);
               cursor = scanResult.getCursor();
               List<String> list = scanResult.getResult();
               if (!CollectionUtils.isEmpty(list)) {
                   result.addAll(list);
               }
           } while (!ScanParams.SCAN_POINTER_START.equals(cursor));
           return result;
       } finally {
           if (jedisTool != null) {
               jedisTool.close();
           }
       }
   }

    public boolean exist(String key) {
        return this.redisTemplate.boundValueOps(key).get() != null;
    }

    public void set(String key, Object value) {
        this.redisTemplate.boundValueOps(key).set(value);
    }

    public String get(String key) {
        return this.get(key, String.class);
    }

    public <T> T get(String key, Class<T> clazz) {
        return (T) this.redisTemplate.boundValueOps(key).get();
    }

    /**
     * pipeline批量获取值
     * @param keys
     * @return
     */
    public Map<String, Object> batchGetByKeys(List<String> keys) {
        Map<String, Object> result = Maps.newConcurrentMap();
        if (CollectionUtils.isEmpty(keys)) {
            return result;
        }
        List<Object> batchResult = this.redisTemplate.executePipelined(
                (RedisCallback<Object>) connection -> {
                    keys.forEach(f -> connection.get(f.getBytes(StandardCharsets.UTF_8)));
                    return null;
                });
        if (CollectionUtils.isEmpty(batchResult)) {
            return result;
        }
        keys.forEach(f -> {
            Object value = batchResult.get(keys.indexOf(f));
            if (!Objects.isNull(value)) {
                result.put(f, value);
            }
        });
        return result;
    }

   public boolean spinLock(String lockKey, String requestId, long timeout) {
       return this.spinLock(lockKey, requestId, true, timeout, timeout);
   }

    /**
     * 旋转锁连接，顺带等待
     * @param lockKey
     * @param requestId
     * @param timeout
     * @return boolean
     */
    public boolean spinLockAttachWaiting(String lockKey, String requestId, long timeout) {
       return this.spinLock(lockKey, requestId, false, timeout + 170, timeout);
   }

    public <T> void distributedLock(Supplier<T> supplier, String lockKey, String requestId) {
        try {
            if (this.tryGetDistributedLock(lockKey, requestId, 1_000)) {
                log.info("获取分布式锁，开始业务处理:{}, {}", lockKey, requestId);
                supplier.get();
            }
        } finally {
            this.releaseDistributedLock(lockKey, requestId);
        }
    }

    /**
     * 自旋等待锁
     * @param lockKey
     * @param requestId
     * @param expireTimeOfMilliSecond
     * @param waitTimeout 超过则自动退出
     * @return
     * @throws InterruptedException
     */
   public boolean spinLock(String lockKey, String requestId, boolean interrupted, long expireTimeOfMilliSecond, long waitTimeout) {
       // 请求锁超时时间，纳秒
       long timeout = waitTimeout * 1_000_000;
       // 系统当前时间，纳秒
       long nowTime = System.nanoTime();
       while ((System.nanoTime() - nowTime) < timeout) {
           if (this.tryGetDistributedLock(lockKey, requestId, expireTimeOfMilliSecond)) {
               return true;
           }
           //不做自旋，获取不到锁则直接中断
           if (interrupted) {
               log.info("分布式锁【spinLock】锁已被占用:waitTimeout:{}, lockKey:{}, requestId:{}", waitTimeout, lockKey, requestId);
               return false;
           }
           // 每次请求等待50ms
           try {
               TimeUnit.MILLISECONDS.sleep(50);
           } catch (InterruptedException e) {
               log.error("分布式锁【spinLock】自旋锁,打断线程休眠:{}, {}", e.getMessage(), e.getCause());
           }
       }
       log.error("分布式锁【spinLock】自旋锁等待超时:{}, {}, {}", waitTimeout, lockKey, requestId);
       return false;
   }

    public boolean tryGetDistributedLock(String lockKey, String requestId, long expireTimeOfMilliSecond) {
        JedisTool jedisTool = null;
        boolean var6;
        try {
            jedisTool = new JedisTool();
            String result = jedisTool.getJedis().set(lockKey, requestId, SetParams.setParams().nx().px(expireTimeOfMilliSecond));
            if (!"OK".equals(result)) {
                return false;
            }
            var6 = true;
        } catch (Exception var10) {
            log.error("########## 获取分布式锁异常 lockKey:{}, requestId:{} ##########", new Object[]{lockKey, requestId, var10});
            return false;
        } finally {
            if (jedisTool != null) {
                jedisTool.close();
            }
        }
        return var6;
    }

    public boolean releaseDistributedLock(String lockKey, String requestId) {
        JedisTool jedisTool = null;
        try {
            jedisTool = new JedisTool();
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Object result = jedisTool.getJedis().eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));
            if (RELEASE_SUCCESS.equals(result)) {
                boolean var6 = true;
                return var6;
            }
        } catch (Exception var10) {
            log.error("########## 释放分布式锁异常 lockKey:{}, requestId:{} ##########", new Object[]{lockKey, requestId, var10});
        } finally {
            if (jedisTool != null) {
                jedisTool.close();
            }
        }
        return false;
    }

    /**
     * 给指定的key对应的key-value设置: 什么时候过时
     *
     * 注:过时后，redis会自动删除对应的key-value。
     * 注:若key不存在，那么也会返回false。??
     */
    public boolean expireAt(String key, Date date) {
        log.info("expireAt(...) => key -> {}, date -> {}", key, date);
        return Optional.ofNullable(redisTemplate.expireAt(key, date)).orElseThrow(() -> new RuntimeException("缓存过期时间异常"));
    }

    public boolean expire(String key, Long timeout, TimeUnit unit) {
        log.info("expire(...) => key -> {}, timeout -> {}", key, timeout);
        return Optional.ofNullable(redisTemplate.expire(key, timeout, unit)).orElseThrow(() -> new RuntimeException("缓存过期时间异常"));
    }

    /**
     * 向key对应的hash中，增加一个键值对entryKey-entryValue（覆盖）
     */
    public void hPutIntValue(String key, String entryKey, Object entryValue) {
        log.info("hPut(...) => key -> {}, entryKey -> {}, entryValue -> {}", key, entryKey, entryValue);
        redisTemplate.opsForHash().put(key, entryKey, entryValue);
    }

    /**
     * 向key对应的hash中，增加maps(即: 批量增加entry集)
     */
    public void hPutAll(String key, Map<String, String> maps) {
        log.info("hPutAll(...) => key -> {}, maps -> {}", key, maps);
        redisTemplate.opsForHash().putAll(key, maps);
    }

    /**
     * 当key对应的hash中,不存在entryKey时，才(向key对应的hash中，)增加entryKey-entryValue
     */
    public boolean hPutIfAbsent(String key, String entryKey, Integer entryValue) {
        log.info("hPutIfAbsent(...) => key -> {}, entryKey -> {}, entryValue -> {}", key, entryKey, entryValue);
        return redisTemplate.opsForHash().putIfAbsent(key, entryKey, entryValue);
    }

    /**
     * (批量)删除(key对应的)hash中的对应entryKey-entryValue
     *
     * 注: 1、若redis中不存在对应的key, 则返回0;
     *     2、若要删除的entryKey，在key对应的hash中不存在，在count不会+1, 如:
     *                 RedisUtil.HashOps.hPut("ds", "name", "邓沙利文");
     *                 RedisUtil.HashOps.hPut("ds", "birthday", "1994-02-05");
     *                 RedisUtil.HashOps.hPut("ds", "hobby", "女");
     *                 则调用RedisUtil.HashOps.hDelete("ds", "name", "birthday", "hobby", "non-exist-entryKey")
     *                 的返回结果为3
     * 注: 若(key对应的)hash中的所有entry都被删除了，那么该key也会被删除
     */
    public long hDelete(String key, Object... entryKeys) {
        log.info("hDelete(...) => key -> {}, entryKeys -> {}", key, entryKeys);
        return redisTemplate.opsForHash().delete(key, entryKeys);
    }

    /**
     * 获取到key对应的hash里面的对应字段的值
     *
     * 注: 若redis中不存在对应的key, 则返回null。
     *     若key对应的hash中不存在对应的entryKey, 也会返回null。
     */
    public Object hGet(String key, String entryKey) {
        log.info("hGet(...) => key -> {}, entryKey -> {}", key, entryKey);
        return redisTemplate.opsForHash().get(key, entryKey);
    }

    /**
     * 查看(key对应的)hash中，是否存在entryKey对应的entry
     *
     * 注: 若redis中不存在key,则返回false。
     * 注: 若key对应的hash中不存在对应的entryKey, 也会返回false。
     */
    public boolean hExists(String key, String entryKey) {
        log.info("hDelete(...) => key -> {}, entryKeys -> {}", key, entryKey);
        return redisTemplate.opsForHash().hasKey(key, entryKey);
    }

    /**
     * 增/减(hash中的某个entryValue值) 整数
     *
     * 注: 负数则为减。
     * 注: 若key不存在，那么会自动创建对应的hash,并创建对应的entryKey、entryValue,entryValue的初始值为increment。
     * 注: 若entryKey不存在，那么会自动创建对应的entryValue,entryValue的初始值为increment。
     * 注: 若key对应的value值不支持增/减操作(即: value不是数字)， 那么会
     *     抛出org.springframework.data.redis.RedisSystemException
     */
    public long hIncrBy(String key, Object entryKey, long increment) {
        log.info("hIncrBy(...) => key -> {}, entryKey -> {}, increment -> {}",
                key, entryKey, increment);
        return redisTemplate.opsForHash().increment(key, entryKey, increment);
    }

    /**
     * 获取(key对应的)hash中的所有entry的数量
     *
     * 注: 若redis中不存在对应的key, 则返回值为0
     */
    public long hSize(String key) {
        log.info("hSize(...) => key -> {}", key);
        return redisTemplate.opsForHash().size(key);
    }

    private class JedisTool {
        private JedisConnection jedisConnection;

        private JedisTool() {
            this.jedisConnection = (JedisConnection)jedisConnectionFactory.getConnection();
        }

        public Jedis getJedis() {
            return this.jedisConnection.getNativeConnection();
        }

        public void close() {
            this.jedisConnection.close();
        }
    }

}
