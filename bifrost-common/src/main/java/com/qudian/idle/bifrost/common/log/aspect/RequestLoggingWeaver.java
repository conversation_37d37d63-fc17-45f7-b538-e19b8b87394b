package com.qudian.idle.bifrost.common.log.aspect;

import com.qudian.idle.bifrost.common.dto.HttpRespDTO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.log.RequestLogHandle;
import com.qudian.idle.bifrost.common.log.annotion.RequestLog;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * {@inheritDoc} 请求日志记录切面
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@Aspect
@Slf4j
@Order(2)
@Component
public class RequestLoggingWeaver {

    /**
     * text类型长度限制，超长截断
     */
    private static final int RESPONSE_THRESHOLD = 0xFFFF;
    private ExpressionParser expressionParser = new SpelExpressionParser();
    // 一个工具，类似反射的使用方式，但是是反射没有的功能
    private LocalVariableTableParameterNameDiscoverer localParameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();
    @Resource
    private RequestLogHandle requestLogHandle;
    @Resource(name = "requestLoggingTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Pointcut(value = "@annotation(requestLog)", argNames = "requestLog")
    private void pointcut(RequestLog requestLog) {
    }

    @Around(value = "pointcut(requestLog)", argNames = "joinPoint,requestLog")
    public Object advice(ProceedingJoinPoint joinPoint, RequestLog requestLog) throws Throwable {
        Object result = joinPoint.proceed();
        try {
            // 获取注解
            if (requestLog == null) {
                throw new BizException("[requestLog] 获取注解失败");
            }
            log.info("[requestLog] 类:{} 进入事件日志记录", joinPoint.getTarget().getClass().getSimpleName());

            String traceId = TraceContext.traceId();

            // 解析拦截到的方法对象
            Method method = pointToMethod(joinPoint);

            // 获取方法参数名称列表，这里使用了LocalVariableTableParameterNameDiscoverer对象
            String[] params = localParameterNameDiscoverer.getParameterNames(method);

            // 获取方法参数列表
            Object[] args = joinPoint.getArgs();

            executor.execute(() -> recordLog(requestLog, params, args, result, traceId));
        } catch (Exception e) {
            // 返回业务异常的错误信息
            log.warn("发生错误", e);
        } finally {
            return result;
        }
    }

    private void recordLog(RequestLog requestLog, String[] params, Object[] args, Object result, String traceId) {
        // 初始化spel上下文，这里制作参数的初始化，spring cache中还会有方法名称和返回参数的初始化
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < params.length; i++) {
            context.setVariable(params[i], args[i]);
        }

        BizRequestLogParam bizRequestLogParam = expressionParser.parseExpression(requestLog.bizParam()).getValue(context, BizRequestLogParam.class);

        String requestBody = StringUtils.isBlank(requestLog.requestBody()) ? null : expressionParser.parseExpression(requestLog.requestBody()).getValue(context, String.class);
        String requestUrl = expressionParser.parseExpression(requestLog.requestUrl()).getValue(context, String.class);
        String responseBody = null;
        // 入参对象判断
        // 返回流的特殊情况判断
        if (result instanceof Response) {
            Response response = (Response) result;
            String header = response.header("Content-Type");
            if (response.isSuccessful()) {
                if (!"application/pdf".equals(header)) {
                    try {
                        requestBody = response.body().string();
                    } catch (IOException e) {
                        log.error("get response body fail, exception:", e);
                        return;
                    }
                }
            }
        } else if (result instanceof HttpRespDTO) {
            responseBody = ((HttpRespDTO) result).getResp();
        } else {
            responseBody = (String) result;
        }
        // response截断
        responseBody = StringUtils.truncate(responseBody, RESPONSE_THRESHOLD);

        // 落表
        Object transfer = requestLogHandle.transfer(traceId, requestUrl, requestBody, responseBody, true, bizRequestLogParam);
        requestLogHandle.saveLog(transfer);
    }

    /**
     * 从拦截器参数中获取方法对象
     *
     * @param pjp
     * @return
     * @throws NoSuchMethodException
     */
    private Method pointToMethod(ProceedingJoinPoint pjp) throws NoSuchMethodException {
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        Method method = methodSignature.getMethod();
        Method targetMethod = pjp.getTarget().getClass().getMethod(method.getName(), method.getParameterTypes());
        return targetMethod;
    }

}
