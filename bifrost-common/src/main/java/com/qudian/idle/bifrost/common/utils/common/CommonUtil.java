package com.qudian.idle.bifrost.common.utils.common;

import com.qudian.idle.bifrost.common.enums.EnvironmentEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Component
public class CommonUtil {
    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);
    @Value("${spring.profiles.active}")
    private String profiles;

    public String getProfiles() {
        return profiles;
    }

    public CommonUtil() {
    }

    public boolean isProduction() {
        return this.profiles.equals(EnvironmentEnum.ENV_PROD.getEnvLabel());
    }

    public boolean isTest() {
        return StringUtils.contains(this.profiles, EnvironmentEnum.ENV_TEST.getEnvLabel());
    }

    public boolean isLocalOrDev() {
        return this.profiles.equals(EnvironmentEnum.ENV_LOCAL.getEnvLabel()) || this.profiles.equals(EnvironmentEnum.ENV_DEV.getEnvLabel());
    }

    public boolean isLocal() {
        return this.profiles.equals(EnvironmentEnum.ENV_LOCAL.getEnvLabel());
    }
}
