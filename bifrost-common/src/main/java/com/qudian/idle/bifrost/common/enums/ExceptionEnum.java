package com.qudian.idle.bifrost.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExceptionEnum {
    SUCCESS(0, "成功"),
    FAIL(-1, "失败"),
    PROCESSING(2,"处理中"),

    // 通用异常 1001-1999
    UNKNOWN(1001, "未知异常"),
    SYSTEM_ERR(1002, "ERROR-系统异常"),
    TIME_OUT(1003,"请求超时"),
    REPEAT(1004,"重复调用"),
    PARAMETER_VERIFICATION(1005, "参数校验异常"),
    RATE_LIMIT(1006,"限流异常"),

    // 业务异常 2001-2999
    SMS_CODE_VALIDATE_ERROR(2001, "验证码错误"),
    HIGH_QUANTITY_SMS_REQUEST(2002, "发送短信高频，请稍后请求"),
    HIGH_QUANTITY_EMAIL_REQUEST(2003, "发送邮件高频，请稍后请求"),
    NOT_FOUND_PACKAGE(2004, "未找到包裹信息"),
    CONVERT_ADDRESS_GEO(2005, "地址无法转换GEO"),
    CHANNEL_NOT_SUPPORT(2006, "订单渠道不支持"),

    // 内部运行异常 3001-3999
    JOB_EXECUTING(3001, "任务运行异常"),
    DB_BUSY(3002, "数据库操作异常，请稍后重试"),

    // 外部调用异常 4001-4999
    RPC_INVOKE(4001, "RPC调用异常"),
    THIRD_SYSTEM_ERROR(4002, "第三方系统错误"),
    SECOND_PARTY_ERROR(4003, "调用二方库错误"),

    REGION_UPLOAD_ERROR(4004, "行政区域查询"),
    REGION_QUERY_ERROR(4005, "Region区域查询"),

    REGION_CENTER_ERROR(4006, "Region中心区域查询"),

    AXB_BIND_ERROR(4007, "AXB绑定异常"),
    CHECK_PHONE_ERROR(4008, "校验电话号码格式异常"),

    REGION_QUERY_V2_ERROR(4009, "Region区域查询V2"),
    AXB_CALL_LOG_ERROR(4010, "查询AXB通话记录异常"),

    //Auth 5001-5999
    JWT_PARAM_VERIFY_ERROR(5001, "参数校验错误"),
    JWT_UNKNOWN_PLATFORM(5002, "未知平台"),
    JWT_VERIFY_FAIL(5003, "鉴权失败"),
    KICK_ALL_ERROR(5004, "账号全平台下线失败")
    ,;

    private Integer code;
    private String msg;
}
