package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseRespDTO implements Serializable {
    private String ask;
    private String message;
    @J<PERSON>NField(name = "Error")
    private Error error;
    @JSONField(name = "error_code")
    private String errorCode;
}
