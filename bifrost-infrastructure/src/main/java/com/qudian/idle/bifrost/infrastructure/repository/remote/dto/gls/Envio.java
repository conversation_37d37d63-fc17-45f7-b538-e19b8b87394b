package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Data;

import javax.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Envio {
    @XmlAttribute(name = "codbarras")
    private String codbarras;

    @XmlElement(name = "Fecha")
    private String fecha;

    @XmlElement(name = "Portes")
    private String portes;

    @XmlElement(name = "Servicio")
    private String servicio;

    @XmlElement(name = "Horario")
    private String horario;

    @XmlElement(name = "Bultos")
    private Integer bultos;

    @XmlElement(name = "Peso")
    private Integer peso;

    @XmlElement(name = "Retorno")
    private Integer retorno;

    @XmlElement(name = "Pod")
    private String pod;

    @XmlElement(name = "Remite")
    private Remite remite;

    @XmlElement(name = "Destinatario")
    private Remite destinatario;

    @XmlElement(name = "Referencias")
    private Referencias referencias;

    @XmlElement(name = "Importes")
    private Importes importes;

    @XmlElement(name = "Resultado")
    private Resultado resultado;

    @XmlElement(name = "DNINomb")
    private String DNINomb;
}
