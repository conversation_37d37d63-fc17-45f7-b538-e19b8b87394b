package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRequestDTO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SearchRoutesReqDTO {
    /**
     * 查询号类别:
     * 1:根据顺丰运单号查询,trackingNumber将被当作顺丰运单号处理
     * 2:根据客户订单号查询,trackingNumber将被当作客户订单号处理
     */
    private Integer trackingType;
    /**
     * 查询号:
     * trackingType=1,则此值为顺丰运单号
     * 如果trackingType=2,则此值为客户订单号
     */
    private List<String> trackingNumber;
}
