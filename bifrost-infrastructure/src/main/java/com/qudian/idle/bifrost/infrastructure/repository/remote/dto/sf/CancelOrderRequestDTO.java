package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CancelOrderRequestDTO {

    //必填、响应报文的语言， 缺省值为zh-CN，目前支持以下值zh-CN 表示中文简体， zh-TW或zh-HK或 zh-MO表示中文繁体， en表示英文
    private String language;

    //必填、客户订单号， 客户订单号在客户系统中是唯一的， 客户订单号在客户系统中是唯一的， 客户订单号在客户系统中是唯一的
    private String orderId;
    //客户订单操作标识: 1:确认 (丰桥下订单接口默认自动确认，不需客户重复确认，该操作用在其它非自动确认的场景) 2:取消

    private Integer dealType;


}
