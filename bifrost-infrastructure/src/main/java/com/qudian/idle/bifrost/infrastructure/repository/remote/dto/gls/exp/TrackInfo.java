package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class TrackInfo {
    /**
     * 时间，+2时区
     */
    @XmlElement(name = "fecha")
    private String fecha;

    @XmlElement(name = "tipo")
    private String tipo;

    @XmlElement(name = "plaza")
    private Integer plaza;

    /**
     * 事件
     */
    @XmlElement(name = "evento")
    private String evento;

    @XmlElement(name = "prioridad")
    private Integer prioridad;

    /**
     * 事件代码
     */
    @XmlElement(name = "codigo")
    private Integer codigo;

    /**
     * 地点
     */
    @XmlElement(name = "nombreplaza")
    private String nombreplaza;

}
