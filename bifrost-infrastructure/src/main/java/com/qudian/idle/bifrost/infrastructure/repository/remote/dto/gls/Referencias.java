package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.*;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
@Accessors(chain = true)
public class Referencias {
    @XmlElement(name = "Referencia")
    private List<Referencia> referencia;

    // Getters and setters
    // toString method for printing the object
}
