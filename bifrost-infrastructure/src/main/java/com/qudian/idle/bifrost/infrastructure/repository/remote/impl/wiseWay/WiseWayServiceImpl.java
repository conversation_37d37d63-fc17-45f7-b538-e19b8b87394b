package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.wiseWay;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.idle.bifrost.common.config.WiseWayApiConfig;
import com.qudian.idle.bifrost.common.constant.AusTransportConstant;
import com.qudian.idle.bifrost.common.constant.FileExtendConstant;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.utils.HttpClient;
import com.qudian.idle.bifrost.common.utils.common.SignUtils;
import com.qudian.idle.bifrost.infrastructure.repository.remote.CommonBaseInfoService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.idle.bifrost.infrastructure.repository.remote.WiseWayService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.AusTransPortAccountInfoDTO;
import com.qudian.idle.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.BaseErrorVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.CreateOrderDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.CreateOrderDTOReq;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.CreateOrderRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.PrintLabelVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.PrintLabelsDTOReq;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackReqVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayResponseBaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class WiseWayServiceImpl implements WiseWayService {

    @Autowired
    private WiseWayApiConfig wiseWayApiConfig;
    @Autowired
    private CommonBaseInfoService commonBaseInfoService;
    @Autowired
    private ToolSrvRemote toolSrvRemote;
    private static final String CREATE_URL = "/services/shipper/orderLabels";
    private static final String CANCEL_URL = "/services/shipper/deleteOrder";

    private static final String DISPATCH_URL = "/services/shipper/manifests";


    private static final String LABEL_URL = "/services/shipper/labels";
    private static final String TRACK_URL = "/services/shipper/trackingEvents";

    private static final String DEFAULT_ITEM_DESC = "General";

    @Value("${wise.default.email:<EMAIL>}")
    private String defaultEmail;
    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private HttpClient client;

    @Override
    public PushSingleTransportResponseVO pushShipment(PushSingleTransportReqVO reqVO) {
        CreateOrderDTO createOrderDTO = buildCreateOrderBusinessInfo(reqVO);
        String formatTime = generateTime();
        String url = wiseWayApiConfig.getHost() + CREATE_URL;

        // 构建签名
        Map<String, String> headerMap = buildHeader(formatTime, url, "POST");
        CreateOrderDTOReq createOrderDTOReq = new CreateOrderDTOReq();
        createOrderDTOReq.setOrders(Collections.singletonList(createOrderDTO));

        BizRequestLogParam logParam = BizRequestLogParam.builder()
            .orderNumbers(Lists.newArrayList(reqVO.getShipment().getOrderNumber()))
            .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
            .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
            .build();

        String s = client.postJsonData(url, JSON.toJSONString(createOrderDTOReq), headerMap, logParam);
        WiseWayResponseBaseVO wiseWayResponseBaseVO = JSON.parseObject(s, WiseWayResponseBaseVO.class);
        CreateOrderRespVO orderRespVO = processCreateOrderResp(wiseWayResponseBaseVO);
        return PushSingleTransportResponseVO.builder().orderNumber(reqVO.getShipment().getOrderNumber())
                .articleId(String.join(",", orderRespVO.getTrackingNos())).consignmentId(orderRespVO.getOrderId())
                .build();

    }

    @NotNull
    private Map<String, String> buildHeader(String formatTime, String url, String type) {
        String sign = SignUtils.createWiseWaySign(wiseWayApiConfig.getSecret(), url, type, formatTime);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Auth " + wiseWayApiConfig.getAppkey() + ":" + sign);
        headerMap.put("X-Auth-Date", formatTime);
        headerMap.put("Content-Type", "application/json");
        return headerMap;
    }

    @NotNull
    private static String generateTime() {
        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT")); // 设置时区为GMT
        return sdf.format(cd.getTime());
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        String orderNumber = reqVO.getOrderNumber();
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, 0);
        // 请求wiseWay
        PrintLabelsDTOReq printLabelsDTOReq = new PrintLabelsDTOReq();
        printLabelsDTOReq.setOrderIds(Collections.singletonList(orderTransportPO.getConsignmentId()));
        String formatTime = generateTime();
        String url = wiseWayApiConfig.getHost() + LABEL_URL;
        // 构建签名
        Map<String, String> headerMap = buildHeader(formatTime, url, "POST");
        BizRequestLogParam logParam = BizRequestLogParam.builder()
                .orderNumbers(Lists.newArrayList(reqVO.getOrderNumber()))
                .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
                .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
                .build();
        String s = client.postJsonData(url, JSON.toJSONString(printLabelsDTOReq), headerMap,logParam);
        List<PrintLabelVO> printLabelVOS = processResult(s, PrintLabelVO.class);
        if (CollectionUtils.isNotEmpty(printLabelVOS)) {
            PrintLabelVO printLabelVO = printLabelVOS.get(0);
            UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(Base64.getDecoder().decode(printLabelVO.getLabelContent()), DateUtil.format(new Date(), "yyyyMMhhHHmmssSSS") + FileExtendConstant.PDF);
            return PrintLabelResponseVO.builder().objectKey(uploadFileRespVO.getData().getObject_key()).signedUrl(uploadFileRespVO.getData()
                    .getSigned_url()).expiredAt(uploadFileRespVO.getData().getExpired_at()).build();
        }
        throw new BizException("获取面单失败");

    }

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(reqVO.getOrderNumber(), 0);
        String formatTime = generateTime();
        String url = wiseWayApiConfig.getHost() + CANCEL_URL;
        Map<String, String> headerMap = buildHeader(formatTime, url, "DELETE");
        BizRequestLogParam logParam = BizRequestLogParam.builder()
                .orderNumbers(Lists.newArrayList(reqVO.getOrderNumber()))
                .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
                .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
                .build();

        String s = client.deleteJsonData(url, JSON.toJSONString(Collections.singletonList(orderTransportPO.getConsignmentId())), headerMap, logParam);
        WiseWayResponseBaseVO wiseWayResponseBaseVO = JSON.parseObject(s, WiseWayResponseBaseVO.class);
        processCancelOrderResult(wiseWayResponseBaseVO);
    }

    @Override
    public void dispatchSingleTransport(SingleOrderTransportReqVO singleOrderTransportReqVO) {
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(singleOrderTransportReqVO.getOrderNumber(), 0);
        String formatTime = generateTime();
        String url = wiseWayApiConfig.getHost() + DISPATCH_URL;
        Map<String, String> headerMap = buildHeader(formatTime, url, "POST");
        String consignmentId = orderTransportPO.getConsignmentId();
        BizRequestLogParam logParam = BizRequestLogParam.builder()
                .orderNumbers(Lists.newArrayList(singleOrderTransportReqVO.getOrderNumber()))
                .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
                .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
                .build();
        String requestJsonStr = JSON.toJSONString(Collections.singletonList(consignmentId));
        String s = client.postJsonData(url, requestJsonStr, headerMap, logParam);
        processResult(s,CreateOrderRespVO.class);

    }

    @Override
    public List<WiseWayOrderTrackRespVO> queryOrderTrack(WiseWayOrderTrackReqVO reqVO) {
        String formatTime = generateTime();
        String url = wiseWayApiConfig.getHost() + TRACK_URL;

        // 构建签名
        Map<String, String> headerMap = buildHeader(formatTime, url, "POST");
        BizRequestLogParam logParam = BizRequestLogParam.builder()
                .orderNumbers(reqVO.getOrderNums())
                .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
                .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
                .build();

        String s = client.postJsonData(url, JSON.toJSONString(reqVO.getTrackNums()), headerMap, logParam);
        WiseWayResponseBaseVO<WiseWayOrderTrackRespVO> wiseWayResponseBaseVO = JSON.parseObject(s, new TypeReference<WiseWayResponseBaseVO<WiseWayOrderTrackRespVO>>() {
        });
        if ("Failure".equals(wiseWayResponseBaseVO.getStatus())) {
            log.error("query wiseway track fail, reqVO:{}, resp:{}", JSON.toJSONString(reqVO), JSON.toJSONString(wiseWayResponseBaseVO));
            return Collections.emptyList();
        }
        return wiseWayResponseBaseVO.getData();
    }

    private CreateOrderDTO buildCreateOrderBusinessInfo(PushSingleTransportReqVO reqVO) {
        PushSingleTransportReqVO.RecipientInfo recipientInfo = reqVO.getShipment().getRecipientInfo();
        PushSingleTransportReqVO.SenderInfo senderInfo = reqVO.getShipment().getSenderInfo();
        CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder = CreateOrderDTO.builder().referenceNo(reqVO.getShipment().getOrderNumber());
        List<PushSingleTransportReqVO.ItemInfo> itemInfo = reqVO.getShipment().getItemInfo();
        setUpReceiptAddress(recipientInfo, createOrderDTOBuilder);
        String city = commonBaseInfoService.processCityCode(recipientInfo);
        createOrderDTOBuilder.city(city);
        createOrderDTOBuilder.state(recipientInfo.getReceiptProvince());
        createOrderDTOBuilder.postcode(recipientInfo.getReceiptPostcode());
        createOrderDTOBuilder.shipperName(senderInfo.getSender());
        createOrderDTOBuilder.recipientName(recipientInfo.getRecipient());
        createOrderDTOBuilder.phone(recipientInfo.getRecipientMobilePhone());
        createOrderDTOBuilder.country("AU");
        // 设置仓库映射关系
        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = commonBaseInfoService.getAccountInfoByState(reqVO.getWarehouseLocationState());
        if (Objects.isNull(ausTransPortAccountInfoDTO) || StringUtils.isBlank(ausTransPortAccountInfoDTO.getTargetWarehouseLocationState())) {
            createOrderDTOBuilder.finalMileInjectionLocation("SYD");
        } else {
            createOrderDTOBuilder.finalMileInjectionLocation(ausTransPortAccountInfoDTO.getTargetWarehouseLocationState());
        }
        createOrderDTOBuilder.serviceType("AUPOST");
        String email = recipientInfo.getEmail();
        if (StringUtils.isBlank(email)) {
            email = defaultEmail;
        }
        createOrderDTOBuilder.email(email);
        setUpVolume(createOrderDTOBuilder, itemInfo);
        return createOrderDTOBuilder.build();
    }

    private void setUpReceiptAddress(PushSingleTransportReqVO.RecipientInfo recipientInfo, CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder) {
        List<String> receipentAddressList = commonBaseInfoService.splitAddressInfo(recipientInfo.getRecipientAddress());
        if (receipentAddressList.size() <= 1) {
            createOrderDTOBuilder.addressLine1(receipentAddressList.get(0));
        } else if (receipentAddressList.size() == 2) {
            createOrderDTOBuilder.addressLine1(receipentAddressList.get(0));
            createOrderDTOBuilder.addressLine2(receipentAddressList.get(1));
        } else {
            createOrderDTOBuilder.addressLine1(receipentAddressList.get(0));
            createOrderDTOBuilder.addressLine2(receipentAddressList.get(1));
            createOrderDTOBuilder.addressLine3(receipentAddressList.get(2));
        }
    }

    private void setUpVolume(CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder, List<PushSingleTransportReqVO.ItemInfo> itemInfoList) {
        // 默认处理
        createOrderDTOBuilder.goodsDescription(DEFAULT_ITEM_DESC);
        CreateOrderDTO.OrderItem orderItem = new CreateOrderDTO.OrderItem();
        orderItem.setDescription(DEFAULT_ITEM_DESC);
        orderItem.setItemCount(1);
        orderItem.setUnitValue(1D);
        orderItem.setWeight(AusTransportConstant.DEFAULT_WEIGHT);
        orderItem.setHeight(AusTransportConstant.DEFAULT_HEIGHT.intValue());
        orderItem.setLength(AusTransportConstant.DEFAULT_LENGTH.intValue());
        orderItem.setWidth(AusTransportConstant.DEFAULT_WIDTH.intValue());
        createOrderDTOBuilder.orderItems(Collections.singletonList(orderItem));
        createOrderDTOBuilder.goodsValue(1D);
        createOrderDTOBuilder.dimensionUnit("cm");
        createOrderDTOBuilder.weightUnit("KG");
    }

    public CreateOrderRespVO processCreateOrderResp(WiseWayResponseBaseVO wiseWayResponseBaseVO) {
        List data = wiseWayResponseBaseVO.getData();
        if (CollectionUtils.isEmpty(data)) {
            throw new BizException("渠道商异常，请重新下单");
        }
        List<CreateOrderRespVO> createOrderRespVOList = JSON.parseArray(JSON.toJSONString(wiseWayResponseBaseVO.getData()), CreateOrderRespVO.class);
        CreateOrderRespVO orderRespVO = createOrderRespVOList.get(0);
        if (wiseWayResponseBaseVO.getStatus().equals("Success")) {
            return orderRespVO;
        } else {
            // error
            List<BaseErrorVO> errors = orderRespVO.getErrors();
            if (CollectionUtils.isEmpty(errors)) {
                throw new BizException("下单异常，请重试");
            }
            throw new BizException(Integer.valueOf(errors.get(0).getCode()), errors.get(0).getMessage());
        }
    }

    public void processCancelOrderResult(WiseWayResponseBaseVO wiseWayResponseBaseVO) {
        if ("Failure".equals(wiseWayResponseBaseVO.getStatus())) {
            throw new BizException("取消失败，请重试");
        } else {
            log.info("取消的响应结果为:{}", wiseWayResponseBaseVO);
        }
    }

    public static <T> List<T> processResult(String jsonString, Class<T> clazz) {
        Type type = new TypeReference<WiseWayResponseBaseVO<T>>() {
        }.getType();
        WiseWayResponseBaseVO<T> response = JSON.parseObject(jsonString, type);
        if ("Success".equals(response.getStatus())) {
            return JSON.parseArray(JSON.toJSONString(response.getData()),clazz);
        } else {
            log.error("响应的参数为{}", jsonString);
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(response.getData().get(0)));
            List errors = (List) jsonObject.get("errors");
            if (CollectionUtils.isNotEmpty(errors)) {
                BaseErrorVO baseErrorVO = JSON.parseObject(JSON.toJSONString(errors.get(0)), BaseErrorVO.class);
                throw new BizException((Integer.valueOf(baseErrorVO.getCode())), baseErrorVO.getMessage());
            }
            throw new BizException("请求wiseWay解析失败,请重试");
        }
    }
}

