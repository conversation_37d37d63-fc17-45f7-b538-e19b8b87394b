package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> Huang
 * @date 2025/8/29
 */

@Configuration
@Slf4j
public class ThreadPoolConfig {

    /**
     * 日志保存专用线程池（IO密集型任务）
     */
    @Bean("logSaveThreadPool")
    public ExecutorService logSaveThreadPool() {
        // 1. 核心线程数：IO密集型任务，建议设置为 CPU核心数 * 2
//        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        int corePoolSize = 5 * 2;

        // 2. 最大线程数：避免无限制增长，通常为核心线程数的 2-4 倍
        int maximumPoolSize = corePoolSize * 2;

        // 3. 空闲线程存活时间：IO任务可能耗时较长，设置为 60s 避免频繁创建销毁线程
        long keepAliveTime = 60L;

        // 4. 工作队列：使用有界队列，避免任务堆积导致OOM（容量根据业务量调整，如1000）
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(1000);

        // 5. 线程工厂：自定义线程名称，便于日志排查
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("log-save-thread-" + counter.getAndIncrement());
                thread.setDaemon(true); // 守护线程：主线程退出时自动销毁
                return thread;
            }
        };

        // 6. 拒绝策略：队列满时的处理方案（日志任务建议记录后降级处理）
        RejectedExecutionHandler handler = new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                // 记录拒绝日志，避免任务丢失（可考虑写入本地文件或MQ后续重试）
                log.error("日志保存线程池任务已满，已拒绝新任务！当前队列大小：{}", executor.getQueue().size());
            }
        };

        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                threadFactory,
                handler
        );
    }

    public static void main(String[] args) {
        System.out.println(Runtime.getRuntime().availableProcessors() );
    }
}

