package com.qudian.idle.bifrost.infrastructure.repository.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ApiInvokeLogPO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description 接口调用日志 Mapper
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface ApiInvokeLogMapper extends BaseMapper<ApiInvokeLogPO> {
    @Select({"<script>",
            "SELECT * from api_invoke_log t where 1=1 ",
//			DATE_FORMAT(t.created_time,'%Y-%m-%d %H:%i:%s') created_time
//			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(name)' > and t.name LIKE CONCAT('%',#{name},'%') </if>",
//          "<if test='status!=null' > and t.status = #{status} </if>",
//			"<if test='createdStartTime != null' > and t.created_time &gt;= #{createdStartTime} </if>",
//			"<if test='createdEndTime != null' > and t.created_time &lt;= #{createdEndTime} </if>",
//			"<if test='!@org.springframework.util.CollectionUtils@isEmpty(idList)' > ",
//			" t.id IN"+ "<foreach collection='idList' item='id' open='(' separator=',' close=')'> #{id}</foreach>",
//			"</if>",
            "order by t.id desc",
            "</script>"})
    List<ApiInvokeLogPO> queryList(ApiInvokeLogPO listReqVO);

}
