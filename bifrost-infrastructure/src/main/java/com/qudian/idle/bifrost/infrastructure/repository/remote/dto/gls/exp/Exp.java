package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * @Author: yangxinye
 * @Date: 2024/4/10
 * @Version: 1.0.0
 **/
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class Exp {

    @XmlElement(name = "fecha")
    private String fecha;


    @XmlElementWrapper(name = "tracking_list")
    @XmlElement(name = "tracking")
    private List<TrackInfo> trackInfos;
}
