package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/17
 **/
@Data
public class WiseWayOrderTrackRespVO {

    private String status;
    private String trackingNo;
    private List<Event> events;

    @Data
    public static class Event {

        private String trackingNo;
        private Date eventTime;
        private String eventCode;
        private String activity;
        private String location;
    }
}
