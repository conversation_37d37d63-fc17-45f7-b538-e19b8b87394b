package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderRequestDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.SfBaseResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Component
@Slf4j
public class CreateOrderHandler extends AbstractSfApiTemplate<CreateExpressReqVO,CreateOrderRequestDTO, CreateExpressRespVO> {

    @Override
    public SfApiCodeEnum support() {
        return SfApiCodeEnum.CREATE_ORDER;
    }

    @Override
    protected CreateExpressRespVO parseResponse(CreateExpressReqVO createExpressReqVO, String responseBody) {
        String requestId = createExpressReqVO.getRequestId();
        // 解析创建订单的响应（仅关注当前接口的响应处理）
        SfBaseResponseDTO baseResponse = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
        if (baseResponse == null || StrUtil.isBlank(baseResponse.getApiResultData())) {
            throw new BizException("创建订单响应格式异常, requestID=" + requestId);
        }

        CreateOrderResponseDTO result = JSON.parseObject(baseResponse.getApiResultData(), CreateOrderResponseDTO.class);
        if (result == null || !result.isSuccess() || result.getMsgData() == null) {
            throw new BizException("创建订单失败: " + result.getErrorCode() + ":" + result.getErrorMsg()
                    + ", requestID=" + requestId);
        }

        String waybillNo = "";
        try {
            waybillNo = result.getMsgData().getWaybillNoInfoList().get(0).getWaybillNo();
        } catch (Exception e) {
            throw new RuntimeException("获取运单号出错");
        }
        return CreateExpressRespVO.builder().orderNo(createExpressReqVO.getOrderNo()).waybillNo(waybillNo).build();

    }

    @Override
    protected CreateOrderRequestDTO convert(CreateExpressReqVO createExpressReqVO) {
        CreateOrderRequestDTO createOrderRequestDTO = CreateOrderRequestDTO.builder().build();
        createOrderRequestDTO.setLanguage("zh-CN");
        createOrderRequestDTO.setOrderId(createExpressReqVO.getExpressOrderNo());
        createOrderRequestDTO.setSendStartTm(createExpressReqVO.getSendStartTm());

        //货物
        String cargoName = createExpressReqVO.getCargoDetails().get(0).getName();
        createOrderRequestDTO.setCargoDesc(cargoName);
        List<CreateOrderRequestDTO.CargoDetail> cargoDetails = Arrays.asList(CreateOrderRequestDTO.CargoDetail.builder().name(cargoName).build());
        createOrderRequestDTO.setCargoDetails(cargoDetails);

        //设置发件人
        CreateExpressReqVO.ContactInfo sendContactVO = createExpressReqVO.getSendContactInfo();
        CreateOrderRequestDTO.ContactInfo sendContactInfo = CreateOrderRequestDTO.ContactInfo.builder()
                .address(sendContactVO.getAddress()).city(sendContactVO.getCity()).contact(sendContactVO.getContact())
                .contactType(1).mobile(sendContactVO.getMobile()).province(sendContactVO.getProvince()).country("CN").build();

        //设置收货人
        CreateExpressReqVO.ContactInfo destContactVO = createExpressReqVO.getDestContactInfo();
        CreateOrderRequestDTO.ContactInfo receiveContactInfo = CreateOrderRequestDTO.ContactInfo.builder()
                .address(destContactVO.getAddress()).city(destContactVO.getCity()).contact(destContactVO.getContact())
                .contactType(2).mobile(destContactVO.getMobile()).province(destContactVO.getProvince()).country("CN").build();

        List<CreateOrderRequestDTO.ContactInfo> contactInfoList = new ArrayList<>();
        contactInfoList.add(sendContactInfo);
        contactInfoList.add(receiveContactInfo);
        createOrderRequestDTO.setContactInfoList(contactInfoList);

        return createOrderRequestDTO;
    }

}
