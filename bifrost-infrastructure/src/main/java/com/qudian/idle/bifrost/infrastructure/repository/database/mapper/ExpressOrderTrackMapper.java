package com.qudian.idle.bifrost.infrastructure.repository.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderTrackPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * @description 快递订单轨迹 Mapper
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface ExpressOrderTrackMapper extends BaseMapper<ExpressOrderTrackPO> {
    @Select({"<script>",
            "SELECT * from express_order_track t where 1=1 ",
            "and t.delete_flag=0 ",
//			DATE_FORMAT(t.created_time,'%Y-%m-%d %H:%i:%s') created_time
//			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(name)' > and t.name LIKE CONCAT('%',#{name},'%') </if>",
//          "<if test='status!=null' > and t.status = #{status} </if>",
//			"<if test='createdStartTime != null' > and t.created_time &gt;= #{createdStartTime} </if>",
//			"<if test='createdEndTime != null' > and t.created_time &lt;= #{createdEndTime} </if>",
//			"<if test='!@org.springframework.util.CollectionUtils@isEmpty(idList)' > ",
//			" t.id IN"+ "<foreach collection='idList' item='id' open='(' separator=',' close=')'> #{id}</foreach>",
//			"</if>",
            "order by t.id desc",
            "</script>"})
    List<ExpressOrderTrackPO> queryList(ExpressOrderTrackPO listReqVO);

    @Select({"<script>",
            "SELECT t1.* ",
            "FROM express_order_track t1 INNER JOIN ( " ,
            "SELECT waybill_no, MAX(track_time) as max_track_time " ,
            "FROM express_order_track WHERE delete_flag = 0 ",
            "AND waybill_no IN ",
            "  <foreach collection='waybillNos' item='item' separator=',' open='(' close=')'>",
            "    #{item}" ,
            "  </foreach>" ,
            " GROUP BY waybill_no ",
            " ) t2 ON t1.waybill_no = t2.waybill_no AND t1.track_time = t2.max_track_time AND t1.delete_flag = 0 ",
            " ORDER BY t1.track_time DESC ",
            "</script>"})
    List<ExpressOrderTrackPO> listByWaybillNoLatest(@Param("waybillNos") Set<String> waybillNos);
}
