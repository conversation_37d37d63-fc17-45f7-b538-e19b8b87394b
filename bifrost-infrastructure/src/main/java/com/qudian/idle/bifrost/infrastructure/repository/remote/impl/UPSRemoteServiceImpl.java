package com.qudian.idle.bifrost.infrastructure.repository.remote.impl;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.common.enums.ExceptionEnum;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.utils.HttpClient;
import com.qudian.idle.bifrost.common.utils.common.DateTimeUtils;
import com.qudian.idle.bifrost.infrastructure.repository.remote.UPSRemoteService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceReqDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceRespDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.request.TrackFieldRequestDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.request.TrackIdDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.response.TrackDetail;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.response.TrackInfoDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.response.TrackResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.xml.bind.JAXB;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;

@Component
@Slf4j
public class UPSRemoteServiceImpl implements UPSRemoteService {

    @Value("${ups.remote.url:https://secure.shippingapis.com/ShippingAPI.dll?}")
    private String upsRemoteUrl;

    @Value("${ups.remote.userId:314PARCE4385}")
    private String upsRemoteUserId;

    @Value("${ups.remote.password:}")
    private String upsRemotePassword;

    @Value("${ups.remote.revision:1}")
    private Integer revision;

    @Value("${ups.remote.clientIp:127.0.0.1}")
    private String clientIP;

    @Value("${ups.remote.sourceId:XYZ Corp}")
    private String sourceId;
    @Value("${ups.remote.apiMethod:TrackV2}")
    private String apiMethod;
    @Resource
    private HttpClient httpClient;

    @Override
    public List<UPSTransportOrderTraceRespDTO> packageTrack(UPSTransportOrderTraceReqDTO reqDTO, List<String> orderNumberList) {
        TrackFieldRequestDTO requestDTO = new TrackFieldRequestDTO();
        List<TrackIdDTO> trackIdList = new ArrayList<>();
        for (String trackId : reqDTO.getTrackIdList()) {
            TrackIdDTO trackIdDTO = new TrackIdDTO();
            trackIdDTO.setId(trackId);

            trackIdList.add(trackIdDTO);
        }
        requestDTO.setTrackIdList(trackIdList);
        TrackResponseDTO trackResponseDTO = trackFieldRequest(requestDTO, orderNumberList);

        List<UPSTransportOrderTraceRespDTO> respDTOList = new ArrayList<>();
        for (TrackInfoDTO trackInfoDTO : trackResponseDTO.getTrackInfoDTOList()) {
            UPSTransportOrderTraceRespDTO resp = new UPSTransportOrderTraceRespDTO();

            // to response
            resp.setTrackId(trackInfoDTO.getId());
            resp.setStatus(trackInfoDTO.getStatus());
            resp.setStatusCategory(trackInfoDTO.getStatusCategory());
            resp.setStatusSummary(trackInfoDTO.getStatusSummary());

            setTrackSummary(trackInfoDTO, resp);

            setTrackDetails(trackInfoDTO, resp);

            respDTOList.add(resp);
        }

        return respDTOList;
    }

    private void setTrackDetails(TrackInfoDTO trackInfoDTO, UPSTransportOrderTraceRespDTO resp) {
        if (Objects.isNull(trackInfoDTO.getTrackDetailList())) {
            return;
        }
        List<UPSTransportOrderTraceRespDTO.TrackDetail> trackDetailList = new ArrayList<>();
        List<TrackDetail> trackDetails = trackInfoDTO.getTrackDetailList();
        for (TrackDetail trackDetail : trackDetails) {
            UPSTransportOrderTraceRespDTO.TrackDetail item = new UPSTransportOrderTraceRespDTO.TrackDetail();
            item.setEventTime(parseEventTime(trackDetail, trackDetailList));
            item.setEvent(trackDetail.getEvent());
            item.setGmt(trackDetail.getGMT());
            item.setGmtOffset(trackDetail.getGMTOffset());
            item.setEventCity(trackDetail.getEventCity());
            item.setEventState(trackDetail.getEventState());
            item.setEventZIPCode(trackDetail.getEventZIPCode());
            item.setFirmName(trackDetail.getFirmName());
            item.setName(trackDetail.getName());
            trackDetailList.add(item);
        }
        resp.setTrackDetailList(trackDetailList);
    }

    private void setTrackSummary(TrackInfoDTO trackInfoDTO, UPSTransportOrderTraceRespDTO resp) {
        if (Objects.isNull(trackInfoDTO.getTrackSummary())) {
            return;
        }
        UPSTransportOrderTraceRespDTO.TrackDetail trackSummary = new UPSTransportOrderTraceRespDTO.TrackDetail();
        trackSummary.setEventTime(parseEventTime(trackInfoDTO.getTrackSummary(), new ArrayList<>()));
        trackSummary.setEvent(trackInfoDTO.getTrackSummary().getEvent());
        trackSummary.setGmt(trackInfoDTO.getTrackSummary().getGMT());
        trackSummary.setGmtOffset(trackInfoDTO.getTrackSummary().getGMTOffset());
        trackSummary.setEventCity(trackInfoDTO.getTrackSummary().getEventCity());
        trackSummary.setEventState(trackInfoDTO.getTrackSummary().getEventState());
        trackSummary.setEventZIPCode(trackInfoDTO.getTrackSummary().getEventZIPCode());
        trackSummary.setFirmName(trackInfoDTO.getTrackSummary().getFirmName());
        trackSummary.setName(trackInfoDTO.getTrackSummary().getName());
        resp.setTrackSummary(trackSummary);
    }

    private TrackResponseDTO trackFieldRequest(TrackFieldRequestDTO requestDTO, List<String> orderNumberList) {
        requestDTO.setUserId(upsRemoteUserId);
        requestDTO.setPassword(upsRemotePassword);
        requestDTO.setRevision(revision);
        requestDTO.setClientIp(clientIP);
        requestDTO.setSourceId(sourceId);

        // 请求内容
        StringWriter sw = new StringWriter();
        JAXB.marshal(requestDTO, sw);
        String content = sw.toString();
        BizRequestLogParam build = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.SPEED.getName())
            .carrier(CarrierTypeEnum.USPS.getName())
            .orderNumbers(orderNumberList)
            .build();

        try {
            String resp = httpClient.curlToUps(upsRemoteUrl, apiMethod, content, build);
            JAXBContext jaxbContext = JAXBContext.newInstance(TrackResponseDTO.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            StringReader reader = new StringReader(resp);
            TrackResponseDTO data = (TrackResponseDTO) unmarshaller.unmarshal(reader);
            log.info("trackFieldRequest resp={}", JSON.toJSONString(data));
            return data;
        } catch (Exception e) {
            log.error("usps track fail, requestDTO:{}, orderNumbers:{}, exception:", JSON.toJSONString(requestDTO), orderNumberList, e);
            throw new BizException(ExceptionEnum.THIRD_SYSTEM_ERROR);
        }
    }

    private Date parseEventTime(TrackDetail trackDetail, List<UPSTransportOrderTraceRespDTO.TrackDetail> trackDetailList) {

        //如果EventDate、EventTime、GMTOffset为空 则取上一条的时间
        if (ObjectUtils.isEmpty(trackDetail.getEventDate()) || ObjectUtils.isEmpty(trackDetail.getEventTime()) || ObjectUtils.isEmpty(trackDetail.getGMTOffset())) {
            if (!ObjectUtils.isEmpty(trackDetailList)) {
                return trackDetailList.get(trackDetailList.size() - 1).getEventTime();
            }
        }

        try {
            String s = String.format("%s %s", trackDetail.getEventDate(), trackDetail.getEventTime());
            String p = "MMM d, yyyy h:mm aa";
            SimpleDateFormat sf = new SimpleDateFormat(p, Locale.ENGLISH);
            String timeZone = DateTimeUtils.parseGMTOffset(trackDetail.getGMTOffset());
            sf.setTimeZone(TimeZone.getTimeZone(timeZone));

            return sf.parse(s);
        } catch (Exception e) {
            log.info("parseEventTime.exception", e);
            return null;
        }
    }

}
