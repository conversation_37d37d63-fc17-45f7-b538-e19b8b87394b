package com.qudian.idle.bifrost.infrastructure.international;

import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountryDataVO extends BaseResponseVO {
    private static final long serialVersionUID = -9048795456194939254L;

    private Carrier carrier;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder(builderMethodName = "ForwarderBuilder")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Carrier extends CountryDataVO {
        private static final long serialVersionUID = -2366621464739481467L;

        private List<CarrierTypeEnum> supportCarriers;

        private CarrierTypeEnum defaultCarrier;
    }

}
