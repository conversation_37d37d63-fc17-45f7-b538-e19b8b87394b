package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ctt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 原始返回
 * @Author: yang<PERSON>ye
 * @Date: 2024/4/25
 * @Version: 1.0.0
 **/
@Data
@Accessors(chain = true)
public class CttShippingStatusRespDTO implements Serializable {

    @JSONField(name = "shipping_code")
    private String shippingCode;

    @JSONField(name = "shipping_history")
    private ShippingHistory shippingHistory;

    @Data
    public static class ShippingHistory {

        @JSONField(name = "origin_name")
        private String originName;

        @JSONField(name = "destin_name")
        private String destinName;

        @JSONField(name = "allow_managements")
        private Boolean allowManagements;

        @JSONField(name = "management_type")
        private String managementType;

        @JSONField(name = "declared_weight")
        private BigDecimal declaredWeight;

        @JSONField(name = "final_weight")
        private BigDecimal finalWeight;

        @JSONField(name = "events")
        private List<Event> events;

        @Data
        public static class Event {

            @JSONField(name = "code")
            private String code;

            @JSONField(name = "description")
            private String description;

            @JSONField(name = "type")
            private String type;

            /** * 时间 */
            @JSONField(name = "event_date")
            private String eventDate;

            @JSONField(name = "detail")
            private Detail detail;

            @Data
            public static class Detail {

                @JSONField(name = "event_longitude_gps")
                private String eventLongitudeGps;

                @JSONField(name = "destin_province_name")
                private String destinProvinceName;

                @JSONField(name = "item_event_datetime")
                private String itemEventDatetime;

                @JSONField(name = "origin_province_name")
                private String originProvinceName;

                @JSONField(name = "delivery_comments")
                private String deliveryComments;

                @JSONField(name = "event_agency_code")
                private String eventAgencyCode;

                @JSONField(name = "event_latitude_gps")
                private String eventLatitudeGps;
            }
        }
    }
}
