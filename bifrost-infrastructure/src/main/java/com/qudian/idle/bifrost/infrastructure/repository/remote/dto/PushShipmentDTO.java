package com.qudian.idle.bifrost.infrastructure.repository.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushShipmentDTO {
    private String username;
    private String digest;
    private String msgType;
    private String version;
    private List<Shipment> shipments;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Shipment {
        private String sender_reference;
        private From from;
        private To to;
        private List<Item> items;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class From {
        private String name;
        private String country;
        private String phone;
        private String suburb;
        private String state;
        private List<String> lines;
        private String postcode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class To {
        private String name;
        private String country;
        private String phone;
        private String suburb;
        private String state;
        private List<String> lines;
        private String postcode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        private String item_description;
        private String product_id;
        private Double weight;
        private Double length;
        private Double width;
        private Double height;
    }
}
