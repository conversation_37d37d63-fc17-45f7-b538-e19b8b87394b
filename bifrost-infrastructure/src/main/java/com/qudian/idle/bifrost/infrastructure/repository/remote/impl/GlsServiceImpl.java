package com.qudian.idle.bifrost.infrastructure.repository.remote.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.idle.bifrost.common.constant.FileExtendConstant;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.utils.HttpClient;
import com.qudian.idle.bifrost.infrastructure.repository.remote.CommonBaseInfoService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.GlsService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.CancelDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.CancelDocIn;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.CancelResponse;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.DocIn;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.Envio;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.EtiquetaEnvio;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.EtiquetaEnvioV2Response;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.GrabaServicios;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.GrabaServiciosResponse;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.Referencia;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.Referencias;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.Remite;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.Servicios;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp.Exp;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp.GetExpCli;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp.GetExpCliResponseVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.speed.SoapRequestGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GlsServiceImpl implements GlsService {

    @Resource
    private CommonBaseInfoService commonBaseInfoService;
    @Resource
    private SoapRequestGenerator soapRequestGenerator;
    @Resource
    private HttpClient httpClient;

    @Value("${gls.url:https://wsclientes.asmred.com/b2b.asmx?wsdl}")
    private String glsUrl;

    @Value("${gls.uid:}")
    private String glsUid;

    /**
     * 查询延迟的时间
     */
    @Value("${gls.query.time.wait.millis:1000}")
    private Long queryTimeWaitMillis;

    @Resource
    private ToolSrvRemote toolSrvRemote;

    

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        String orderNumber = reqVO.getOrderNumber();
        CancelDTO cancelDTO = new CancelDTO();
        CancelDocIn cancelDocIn = new CancelDocIn();
        Servicios servicios = new Servicios();
        Envio envio = new Envio();
        envio.setCodbarras(orderNumber);
        servicios.setEnvio(envio);
        cancelDocIn.setServicios(servicios);
        cancelDTO.setCancelDocIn(cancelDocIn);
        servicios.setUidcliente(glsUid);
        String request = soapRequestGenerator.generateV2GLsSoapRequest(cancelDTO, CancelDTO.class);
        String result = httpClient.postSoapV2(glsUrl, request, getBizRequestLogParam(reqVO.getOrderNumber()));
        // 格式化结果
        CancelResponse cancelResponse = (CancelResponse) soapRequestGenerator.parseSoapResponse(result, CancelResponse.class);
        Integer code = cancelResponse.getCancelResult().getServicios().getEnvio().getResultado().getReturnCode();
        if (code != 0) {
            throw new BizException(code, cancelResponse.getCancelResult().getServicios().getEnvio().getResultado().getValue());
        }


    }

    @Override
    public PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO) {
        // 创建bean
        GrabaServicios grabaServicios = buildCreateOrderBusinessInfo(reqVO);
        String request = soapRequestGenerator.generateV2GLsSoapRequest(grabaServicios, GrabaServicios.class);
        BizRequestLogParam param = getBizRequestLogParam(reqVO.getShipment().getOrderNumber());
        String result = httpClient.postSoapV2(glsUrl, request, param);
        // 格式化结果
        GrabaServiciosResponse grabaServiciosResponse = (GrabaServiciosResponse) soapRequestGenerator.parseSoapResponse(result, GrabaServiciosResponse.class);
        GrabaServiciosResponse.Envio envio = grabaServiciosResponse.getGrabaServiciosResult().getServicios().getEnvio();
        if (envio.getResultado().getReturnCode() == 0) {
            // 成功 否则失败
            String codbarras = envio.getCodbarras();
            PushSingleTransportResponseVO pushSingleTransportResponseVO = new PushSingleTransportResponseVO();
            pushSingleTransportResponseVO.setArticleId(codbarras);
            pushSingleTransportResponseVO.setConsignmentId(codbarras);
            pushSingleTransportResponseVO.setOrderNumber(reqVO.getShipment().getOrderNumber());
            pushSingleTransportResponseVO.setOrigin(reqVO.getOrigin());
            pushSingleTransportResponseVO.setState(reqVO.getWarehouseLocationState());
            return pushSingleTransportResponseVO;
        } else {
            // 失败
            throw new BizException(envio.getResultado().getReturnCode(), "gls异常");
        }
    }

    private static BizRequestLogParam getBizRequestLogParam(String orderNumber) {
        return BizRequestLogParam.builder()
                .forwarderType(CarrierTypeEnum.GLS.getName())
                .carrier(CarrierTypeEnum.GLS.getName())
                .orderNumbers(Collections.singletonList(orderNumber))
                .build();
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        EtiquetaEnvio etiquetaEnvio = new EtiquetaEnvio();
        etiquetaEnvio.setUidCliente(glsUid);
        etiquetaEnvio.setCodigo(reqVO.getOrderNumber());
        etiquetaEnvio.setTipoEtiqueta("PDF");
        String request = soapRequestGenerator.generateV2GLsSoapRequest(etiquetaEnvio, EtiquetaEnvio.class);
        String result = httpClient.postSoapV2(glsUrl, request, getBizRequestLogParam(reqVO.getOrderNumber()));
        EtiquetaEnvioV2Response etiquetaEnvioV2Response = (EtiquetaEnvioV2Response) soapRequestGenerator.parseSoapResponse(result, EtiquetaEnvioV2Response.class);
        String pdfBase64String = Optional.ofNullable(etiquetaEnvioV2Response).map(EtiquetaEnvioV2Response::getEtiquetaEnvioV2Result)
                .map(EtiquetaEnvioV2Response.EtiquetaEnvioV2Result::getBase64Binary)
                .orElseThrow(() -> new BizException("未获取到对应的pdf，请重试"));
        try {
            // 将base64编码的字符串解码为字节数组
            byte[] decodedBytes = Base64.getDecoder().decode(pdfBase64String);
            LocalDateTime now = LocalDateTime.now();
            String format = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss").format(now);
            String fileName = format + FileExtendConstant.PDF;
            // 上传pdf
            UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(decodedBytes, fileName);
            return PrintLabelResponseVO.builder().objectKey(uploadFileRespVO.getData().getObject_key()).signedUrl(uploadFileRespVO.getData()
                    .getSigned_url()).expiredAt(uploadFileRespVO.getData().getExpired_at()).build();
        } catch (Exception e) {
            log.error("解析pdf错误", e);
        }
        return null;
    }
    

    @Override
    public TransportTrackRespVO shippingStatus(ShippingStatusReqDTO reqVO) {
        // warning!! 由于下游不支持批量查询，这里只能for循环查
        TransportTrackRespVO transportTrackRespVO = new TransportTrackRespVO();
        List<TransportTrackRespVO.TrackingResult> results = new ArrayList<>();
        for (ShippingStatusReqDTO.ShippingOrderInfo order : reqVO.getOrderInfoList()) {
            try {
                TransportTrackRespVO.TrackingResult trackingResult = querySingleShippingStatus(order);
                results.add(trackingResult);
                // 防止触发限流
                TimeUnit.MILLISECONDS.sleep(queryTimeWaitMillis);
            } catch (Exception e) {
                log.error("orderInfo:{}, query gls shipping status fail, exception:", order, e);
            }
        }
        transportTrackRespVO.setTrackingResults(results);
        return transportTrackRespVO;
    }

    private TransportTrackRespVO.TrackingResult querySingleShippingStatus(ShippingStatusReqDTO.ShippingOrderInfo order) throws Exception {

        GetExpCli reqDTO = new GetExpCli().setUid(glsUid)
            // 使用gls的单号查
            .setCodigo(order.getTrackingId());
        String getExp = soapRequestGenerator.generateV2GLsSoapRequest(reqDTO, GetExpCli.class);
        // 没有转运服务，不填forwarderType
        BizRequestLogParam bizParam = new BizRequestLogParam()
            .setOrderNumbers(Lists.newArrayList(order.getOrderId()))
            .setCarrier(CarrierTypeEnum.GLS.getName());
        String res = httpClient.postSoapV2(glsUrl, getExp, bizParam);
        if (Objects.isNull(res)) {
            throw new BizException("查询gls轨迹返回null");
        }
        GetExpCliResponseVO getExpCliResponseVO = (GetExpCliResponseVO) soapRequestGenerator.parseSoapResponse(res, GetExpCliResponseVO.class);
        // 转换成trackingResult
        List<Exp> expList = getExpCliResponseVO.getResult()
            .getExpList();
        if (CollectionUtils.isEmpty(expList)) {
            throw new BizException("查询gls轨迹返回exp为空");
        }

        List<TransportTrackRespVO.TrackingResult.Event> eventList = expList.get(0).getTrackInfos().stream()
            .map(trackInfo -> {
                Date date = parseDate(trackInfo.getFecha());
                TransportTrackRespVO.TrackingResult.Event event = new TransportTrackRespVO.TrackingResult.Event()
                    .setEventTime(date.getTime())
                    .setDate(DateUtil.formatDateTime(date))
                    .setEventCode(trackInfo.getCodigo().toString())
                    .setLocation(trackInfo.getNombreplaza())
                    .setDesc(trackInfo.getEvento());
                return event;
            })
            // 根据时间倒序
            .sorted(Comparator.comparing(TransportTrackRespVO.TrackingResult.Event::getEventTime).reversed())
            .collect(Collectors.toList());
        // 如果没有事件也抛异常
        if (eventList.isEmpty()) {
            throw new BizException("查询gls轨迹返回事件为空");
        }

        TransportTrackRespVO.TrackingResult result = new TransportTrackRespVO.TrackingResult();
        result.setOrderNumber(order.getOrderId());
        result.setCarrier(CarrierTypeEnum.GLS.getName());
        result.setEvents(eventList);
        result.setArticleId(order.getTrackingId());
        // 取最新的事件
        if (CollectionUtils.isNotEmpty(eventList)) {
            result.setStatus(eventList.get(0).getEventCode());
        }
        return result;
    }

    private Date parseDate(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        // 西班牙时区
        format.setTimeZone(TimeZone.getTimeZone("Europe/Madrid")); // Set the timezone to UTC+2
        try {
            return format.parse(dateString);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private GrabaServicios buildCreateOrderBusinessInfo(PushSingleTransportReqVO reqVO) {
        PushSingleTransportReqVO.RecipientInfo recipientInfo = reqVO.getShipment().getRecipientInfo();
        PushSingleTransportReqVO.SenderInfo senderInfo = reqVO.getShipment().getSenderInfo();
        Servicios servicios = new Servicios();
        Envio envio = new Envio();

        basicFiledFilling(servicios, envio,glsUid);
        // 重量
        String weight = commonBaseInfoService.calculateTotalWeight(reqVO);
        envio.setPeso(Double.valueOf(weight).intValue());
        // 发件人信息
        int limitSize = 80;
        String es = "34";
        fillSenderInfo(senderInfo, envio, limitSize, es);
        // 收件人信息
        fillReceiverInfo(recipientInfo, envio, limitSize, es);
        // 设置订单号
        Referencia referencia = fillingOrderNumber(reqVO);
        return compositeBean(servicios, envio, referencia);
    }

    private GrabaServicios compositeBean(Servicios servicios, Envio envio, Referencia referencia) {
        GrabaServicios grabaServicios = new GrabaServicios();
        DocIn docIn = new DocIn();
        Referencias referencias = new Referencias().setReferencia(Collections.singletonList(referencia));
        envio.setReferencias(referencias);
        docIn.setServicios(servicios);
        grabaServicios.setDocIn(docIn);
        servicios.setEnvio(envio);
        return grabaServicios;

    }

    @NotNull
    private static Referencia fillingOrderNumber(PushSingleTransportReqVO reqVO) {
        Referencia referencia = new Referencia();
        referencia.setTipo("C");
        referencia.setValue(reqVO.getShipment().getOrderNumber());
        return referencia;
    }

    private static void fillReceiverInfo(PushSingleTransportReqVO.RecipientInfo recipientInfo, Envio envio, int limitSize, String es) {
        Remite receiveInfo = new Remite();
        receiveInfo.setNombre(recipientInfo.getRecipient()).setDireccion(recipientInfo.getRecipientAddress())
                .setPoblacion(StringUtils.isBlank(recipientInfo.getReceiptCity()) ? recipientInfo.getCounty() : recipientInfo.getReceiptCity())
                .setProvincia(recipientInfo.getReceiptProvince().length() >= limitSize ? recipientInfo.getReceiptProvince().substring(0, limitSize) : recipientInfo.getReceiptProvince())
                .setPais(es).setCp(recipientInfo.getReceiptPostcode()).setTelefono(recipientInfo.getRecipientMobilePhone());
        envio.setDestinatario(receiveInfo);
    }

    private static void fillSenderInfo(PushSingleTransportReqVO.SenderInfo senderInfo, Envio envio, int limitSize, String es) {
        Remite remite = new Remite();
        remite.setNombre(senderInfo.getSender()).setDireccion(senderInfo.getSenderAddress().length() >= limitSize ? senderInfo.getSenderAddress().substring(0, limitSize) : senderInfo.getSenderAddress())
                .setPoblacion(StringUtils.isBlank(senderInfo.getSenderCity()) ? senderInfo.getCounty() : senderInfo.getSenderCity())
                .setProvincia(senderInfo.getSenderProvince().length() >= limitSize ? senderInfo.getSenderProvince().substring(0, limitSize) : senderInfo.getSenderProvince())
                .setPais(es).setCp(senderInfo.getSenderPostcode()).setTelefono(senderInfo.getSenderMobilePhone());
        envio.setRemite(remite);
    }

    private static void basicFiledFilling(Servicios servicios, Envio envio,String uidcliente) {
        servicios.setUidcliente(uidcliente);
        // 处理时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/YYYY");
        String time = formatter.format(LocalDate.now());
        envio.setFecha(time);
        // 按月结算
        String monthlySettlement = "P";
        envio.setPortes(monthlySettlement);
        // 服务类型
        String businessParcel = "96";
        envio.setServicio(businessParcel);
        // 取货时间
        String pickUpTime = "18";
        envio.setHorario(pickUpTime);
        // 包裹数量
        Integer packageNum = 1;
        envio.setBultos(packageNum);
        // 是否返回文件
        String notReturnFile = "N";
        envio.setPod(notReturnFile);
        // 是否货到付款 
        Integer notCashOnDelivery = 0;
        envio.setRetorno(notCashOnDelivery);
        // 牵收方式要求
        String dniAndName = "1";
        envio.setDNINomb(dniAndName);
        
    }
}
