package com.qudian.idle.bifrost.infrastructure.repository.database.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.Date;

import java.io.Serializable;

/**
 * 接口调用日志 PO
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain=true)
@TableName("`api_invoke_log`")
public class ApiInvokeLogPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * API类型：1-顺丰
     */
    private Integer platform;
    /**
     * API名称
     */
    private String apiName;
    /**
     * API描述
     */
    private String apiDesc;
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 对象 Id
     */
    private String objectId;
    /**
     * 请求参数
     */
    private String requestParam;
    /**
     * 响应结果
     */
    private String responseResult;
    /**
     * 扩展信息
     */
    private String extJson;
    /**
     * 状态：0-处理中 1-成功 2-失败
     */
    private Integer status;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 调用时间
     */
    private Date invokeTime;
    /**
     * 耗时(毫秒)
     */
    private Integer costTime;

}
