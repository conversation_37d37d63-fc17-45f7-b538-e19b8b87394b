package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.CancelExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.UpdateExpressRespVO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderRequestDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.UpdateOrderRequestDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.SfBaseResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Huang
 * 顺丰 确认和取消是一个接口，通过 dealType区分（客户订单操作标识: 1:确认 (丰桥下订单接口默认自动确认，不需客户重复确认，该操作用在其它非自动确认的场景) 2:取消）
 * @date 2025/8/28
 */
@Component
@Slf4j
public class UpdateOrderHandler extends AbstractSfApiTemplate<UpdateExpressReqVO, UpdateOrderRequestDTO, UpdateExpressRespVO> {

    @Override
    public SfApiCodeEnum support() {
        return SfApiCodeEnum.UPDATE_ORDER;
    }

    @Override
    protected UpdateExpressRespVO parseResponse(UpdateExpressReqVO cancelExpressReqVO, String responseBody) {
        String requestId = cancelExpressReqVO.getRequestId();
        // 解析创建订单的响应（仅关注当前接口的响应处理）
        SfBaseResponseDTO baseResponse = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
        if (baseResponse == null || StrUtil.isBlank(baseResponse.getApiResultData())) {
            throw new BizException("修改快递单响应格式异常, requestID=" + requestId);
        }

        CreateOrderResponseDTO result = JSON.parseObject(baseResponse.getApiResultData(), CreateOrderResponseDTO.class);
        if (result == null || !result.isSuccess() || result.getMsgData() == null) {
            throw new BizException("修改快递单失败: " + result.getErrorCode() + ":" + result.getErrorMsg() + "，requestID=" + requestId + "");
        }

        String waybillNo = "";
        try {
            waybillNo = result.getMsgData().getWaybillNoInfoList().get(0).getWaybillNo();
        } catch (Exception e) {
            throw new RuntimeException("获取运单号出错");
        }
        UpdateExpressRespVO updateExpressRespVO = UpdateExpressRespVO.builder().waybillNo(waybillNo).build();
        return updateExpressRespVO;

    }

    @Override
    protected UpdateOrderRequestDTO convert(UpdateExpressReqVO updateExpressReqVO) {
        UpdateOrderRequestDTO updateOrderRequestDTO = UpdateOrderRequestDTO.builder().build();
        updateOrderRequestDTO.setLanguage("zh-CN");
        updateOrderRequestDTO.setOrderId(updateExpressReqVO.getOrderNo());
        updateOrderRequestDTO.setDealType(1);

        updateOrderRequestDTO.setSendStartTm(updateExpressReqVO.getSendStartTm());

        //货物
        String cargoName = updateExpressReqVO.getCargoDetails().get(0).getName();
        updateOrderRequestDTO.setCargoDesc(cargoName);
        List<UpdateOrderRequestDTO.CargoDetail> cargoDetails = Arrays.asList(UpdateOrderRequestDTO.CargoDetail.builder().name(cargoName).build());
        updateOrderRequestDTO.setCargoDetails(cargoDetails);


        //设置收货人
        CreateExpressReqVO.ContactInfo destContactVO = updateExpressReqVO.getDestContactInfo();
        UpdateOrderRequestDTO.ContactInfo receiveContactInfo = UpdateOrderRequestDTO.ContactInfo.builder()
                .address(destContactVO.getAddress()).city(destContactVO.getCity()).contact(destContactVO.getContact())
                .mobile(destContactVO.getMobile()).province(destContactVO.getProvince()).country("CN").build();
        updateOrderRequestDTO.setDestContactInfo(receiveContactInfo);
        return updateOrderRequestDTO;
    }
}
