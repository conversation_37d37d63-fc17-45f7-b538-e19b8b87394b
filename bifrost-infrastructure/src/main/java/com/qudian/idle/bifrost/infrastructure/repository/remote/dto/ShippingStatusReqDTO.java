package com.qudian.idle.bifrost.infrastructure.repository.remote.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024/4/10
 * @Version: 1.0.0
 **/
@Data
@Accessors(chain = true)
public class ShippingStatusReqDTO {

    private List<ShippingOrderInfo> orderInfoList;


    @Data
    @Accessors(chain = true)
    public static class ShippingOrderInfo {

        private String orderId;

        private String trackingId;
    }



}
