package com.qudian.idle.bifrost.infrastructure.international;

import com.google.common.collect.Maps;
import com.qudian.idle.bifrost.common.enums.CountryCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.EnumMap;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.lme.driver.business.factory.pushAppMsg.PushAppMsgFactory</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/16
 */
@Component
public class CountryFactory {

    @Value("${country.code:au}")
    private String countryCode;

    @Resource
    private List<CountryInterface> countryInterfaceList;

    private final EnumMap<CountryCodeEnum, CountryInterface> COUNTRY_MAPPER = Maps.newEnumMap(CountryCodeEnum.class);

    @PostConstruct
    public void initFactory() {
        COUNTRY_MAPPER.putAll(countryInterfaceList.stream().collect(Collectors.toMap(CountryInterface::support, Function.identity())));
    }

    public CountryDataVO get() {
        CountryInterface countryInterface = getInterface();
        return countryInterface.info(countryInterface.support());
    }

    public CountryDataVO getCountryDataVO(CountryCodeEnum codeEnum){
        CountryInterface countryInterface = COUNTRY_MAPPER.get(codeEnum);
        return countryInterface.info(codeEnum);
    }

    public CountryInterface getInterface() {
        if(StringUtils.isBlank(countryCode)) {
            throw new RuntimeException("countryCode is null");
        }
        CountryCodeEnum countryCodeEnum = CountryCodeEnum.getCountryByCode(countryCode, null);
        if(null == countryCodeEnum) {
            throw new RuntimeException("countryCodeEnum is null");
        }
        CountryInterface countryInterface = COUNTRY_MAPPER.get(countryCodeEnum);
        if(null == countryInterface) {
            throw new RuntimeException("countryInterface is null");
        }
        return countryInterface;
    }

}
