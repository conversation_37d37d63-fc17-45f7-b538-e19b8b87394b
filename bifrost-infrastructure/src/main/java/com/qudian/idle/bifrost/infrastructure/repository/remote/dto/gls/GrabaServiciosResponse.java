package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Data;
import lombok.Getter;

import javax.xml.bind.annotation.*;
import java.util.List;

@XmlRootElement(name = "GrabaServiciosResponse", namespace = "http://www.asmred.com/")
@Getter
public class GrabaServiciosResponse {

    @XmlElement(name = "GrabaServiciosResult",namespace = "http://www.asmred.com/")
    private GrabaServiciosResult grabaServiciosResult;


    @Getter
    public static class GrabaServiciosResult {
        @XmlElement(name = "Servicios",namespace = "")
        private Servicios servicios;
    }

    @Getter
    public static class Servicios {
        @XmlElement(name = "Envio")
        private Envio envio;
    }
    
    @Getter
    public static class Envio {
        @XmlAttribute(name = "codbarras")
        private String codbarras;
        @XmlAttribute(name = "codexp")
        private String codexp;
        @XmlAttribute(name = "uid")
        private String uid;

        @XmlElement(name = "Referencias")
        private Referencias referencias;
        @XmlElement(name = "Resultado")
        private Resultado resultado;
    }
    @Getter
    public static class Resultado {
        @XmlAttribute(name = "return")
        private Integer returnCode;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @Getter
    public static class Referencias {

        @XmlElement(name = "Referencia")
        private List<Referencia> referencias;
    }

    @Getter
   public static class Referencia {

       @XmlAttribute(name = "tipo")
       private String tipo;

       @XmlValue
       private String value;

   }
}



