package com.qudian.idle.bifrost.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.bifrost.api.enums.ExpressOrderStatusEnum;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.database.mapper.ExpressOrderMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Optional;


import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @description 快递订单 Repository
 * @date 2025-08-29
 */
@Repository
public class ExpressOrderRepository extends ServiceImpl<ExpressOrderMapper, ExpressOrderPO> {
    @Resource
    private ExpressOrderMapper expressOrderMapper;

    public Optional<ExpressOrderPO> selectById(Long id) {
        ExpressOrderPO expressOrderPO = expressOrderMapper.selectById(id);
        return Optional.ofNullable(expressOrderPO);
    }

    public boolean deleteById(Long id) {
        return SqlHelper.retBool(expressOrderMapper.deleteById(id));
    }

    public Optional<ExpressOrderPO> selectWaybillNo(String waybillNo) {
        QueryWrapper<ExpressOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExpressOrderPO::getWaybillNo, waybillNo);
        ExpressOrderPO expressOrderPO = expressOrderMapper.selectOne(queryWrapper);
        return Optional.ofNullable(expressOrderPO);
    }

    public List<SearchExpressRespVO> queryList(SearchExpressReqVO searchExpressReqVO) {
        return expressOrderMapper.queryList(searchExpressReqVO);
    }

    public boolean insert(ExpressOrderPO expressOrderPO) {
        return SqlHelper.retBool(expressOrderMapper.insert(expressOrderPO));
    }

    public boolean update(ExpressOrderPO expressOrderPO) {
        return SqlHelper.retBool(expressOrderMapper.updateById(expressOrderPO));
    }

    public Page<ExpressOrderPO> page4TraceQuery(int pageNum, int pageSize, Date startTime) {
        Page<ExpressOrderPO> page = new Page<>(pageNum, pageSize);
        return (Page<ExpressOrderPO>) super.page(page,
                new LambdaQueryWrapper<ExpressOrderPO>()
                        .between(ExpressOrderPO::getStatus, ExpressOrderStatusEnum.ORDERED.getCode(), ExpressOrderStatusEnum.DELIVERING.getCode())
                        .ge(ExpressOrderPO::getUpdatedTime, startTime)
                        .orderByDesc(ExpressOrderPO::getCreatedTime)
        );
    }

    public void batchUpdateStatus(List<String> waybillNos, Integer status) {
        super.update(
                new LambdaUpdateWrapper<ExpressOrderPO>()
                        .set(ExpressOrderPO::getStatus, status)
                        .in(ExpressOrderPO::getWaybillNo, waybillNos)
        );
    }
}
