package com.qudian.idle.bifrost.infrastructure.international;


import com.qudian.idle.bifrost.common.enums.CountryCodeEnum;

/**
 * <p>文件名称:com.qudian.lme.driver.business.factory.pushAppMsg.cell.IPushAppMsg</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/16
 */
public interface CountryInterface {

    /**
     * 获取国家信息
     * @param codeEnum
     * @return
     */
    CountryDataVO info(CountryCodeEnum codeEnum);

    CountryCodeEnum support();

}
