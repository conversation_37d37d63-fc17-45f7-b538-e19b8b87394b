package com.qudian.idle.bifrost.infrastructure.international.country;

import com.qudian.idle.bifrost.infrastructure.international.CountryBase;
import com.qudian.idle.bifrost.infrastructure.international.CountryDataVO;
import com.qudian.idle.bifrost.common.enums.CountryCodeEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <p>文件名称:com.qudian.lme.driver.business.factory.pushAppMsg.cell.PickUpPushAppMsgCell</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/16
 */
@Component
public class Canada extends CountryBase {

    @Override
    public CountryCodeEnum support() {
        return CountryCodeEnum.CANADA;
    }
    @Override
    public CountryDataVO.Carrier getCarrier() {
        return CountryDataVO.Carrier.ForwarderBuilder()
                .supportCarriers(new ArrayList<>())
                .build();
    }



}
