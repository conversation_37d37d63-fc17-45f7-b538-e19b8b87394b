package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ctt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CreateOrderDTO {
    @JSO<PERSON>ield(name = "client_center_code")
    private String clientCenterCode;

    @JSONField(name = "shipping_type_code")
    private String shippingTypeCode;

    @JSONField(name = "client_bar_code")
    private String clientBarCode;

    @JSONField(name = "client_references")
    private List<String> clientReferences;

    @JSONField(name = "shipping_weight_declared")
    private Double shippingWeightDeclared;

    @JSONField(name = "item_count")
    private Integer itemCount;

    @JSONField(name = "custom_origin_name")
    private String customOriginName;

    @JSONField(name = "custom_origin_country_code")
    private String customOriginCountryCode;

    @JSONField(name = "custom_origin_postal_code")
    private String customOriginPostalCode;

    @JSONField(name = "custom_origin_address")
    private String customOriginAddress;

    @JSO<PERSON>ield(name = "custom_origin_town")
    private String customOriginTown;

    @JSONField(name = "sender_name")
    private String senderName;

    @JSONField(name = "sender_country_code")
    private String senderCountryCode;

    @JSONField(name = "sender_postal_code")
    private String senderPostalCode;

    @JSONField(name = "sender_address")
    private String senderAddress;

    @JSONField(name = "sender_town")
    private String senderTown;

    @JSONField(name = "sender_email_notify_address")
    private String senderEmailNotifyAddress;

    @JSONField(name = "sender_phones")
    private List<String> senderPhones;

    @JSONField(name = "recipient_name")
    private String recipientName;

    @JSONField(name = "recipient_country_code")
    private String recipientCountryCode;

    @JSONField(name = "recipient_postal_code")
    private String recipientPostalCode;

    @JSONField(name = "recipient_address")
    private String recipientAddress;

    @JSONField(name = "recipient_town")
    private String recipientTown;

    @JSONField(name = "recipient_email_notify_address")
    private String recipientEmailNotifyAddress;

    @JSONField(name = "recipient_phones")
    private List<String> recipientPhones;

    @JSONField(name = "shipping_date")
    private String shippingDate;

    @JSONField(name = "delivery")
    private Delivery delivery;

    @JSONField(name = "items")
    private List<Item> items;

    @JSONField(name = "additionals")
    private List<Additional> additionals;

    // Getters and setters
    // 省略 getters 和 setters

    public static class Delivery {

        @JSONField(name = "contact_name")
        private String contactName;

        @JSONField(name = "referral_name")
        private String referralName;

        @JSONField(name = "comments")
        private String comments;

        // Getters and setters
        // 省略 getters 和 setters
    }

    public static class Item {

        @JSONField(name = "item_synonym_code")
        private String itemSynonymCode;

        @JSONField(name = "item_weight_declared")
        private Integer itemWeightDeclared;

        @JSONField(name = "item_length_declared")
        private Integer itemLengthDeclared;

        @JSONField(name = "item_width_declared")
        private Integer itemWidthDeclared;

        @JSONField(name = "item_height_declared")
        private Integer itemHeightDeclared;

        @JSONField(name = "item_comments")
        private String itemComments;

        // Getters and setters
        // 省略 getters 和 setters
    }

    public static class Additional {

        @JSONField(name = "additional_code")
        private String additionalCode;

        @JSONField(name = "additional_value")
        private Integer additionalValue;

        @JSONField(name = "additional_flag")
        private Boolean additionalFlag;

        @JSONField(name = "additional_text")
        private String additionalText;

        @JSONField(name = "additional_sub_code")
        private String additionalSubCode;

        // Getters and setters
        // 省略 getters 和 setters
    }
}
