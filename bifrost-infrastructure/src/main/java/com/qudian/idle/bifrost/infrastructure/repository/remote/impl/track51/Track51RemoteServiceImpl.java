package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.track51;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.request.ExpressDeliveryOrderInfo;
import com.qudian.idle.bifrost.common.enums.ExceptionEnum;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.log.BizRequestLogParam;
import com.qudian.idle.bifrost.common.utils.HttpClient;
import com.qudian.idle.bifrost.infrastructure.repository.remote.Track51RemoteService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51.SpecialResponseVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51.SubscribeDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51.Track51AusPostResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51.Track51TransportRequestVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/16
 **/
@Component
@Slf4j
public class Track51RemoteServiceImpl implements Track51RemoteService {

    @Value("${track51.trackings.map.url:/v3/trackings/get}")
    private String trackingsUrl;
    
    @Value("${track51.trackings.subscribe.url:/v4/trackings/batch}")
    private String subscribeTraceUrl;

    @Value("${track51.trackings.get.token:xtr7lvdt-js6h-oqjq-sauv-avj2wpjlvjw9}")
    private String trackingToken;

    @Value("${track51.trackings.url.host.map.host:https://api.51tracking.com}")
    private String trackingUrlHost;
    @Resource
    private HttpClient httpClient;
    private static final Map<String,String> courierMap = new HashMap<>();
    //todo 日后多了，请改成读表
    static {
        courierMap.put("aup","australia-post");
        courierMap.put("gls-es","gls-es");
        courierMap.put("ctt-express","ctt-express");
    }

    @Override
    public List<Track51AusPostResponseDTO> getAusPostData(Track51TransportRequestVO reqVO, List<String> orderIds) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("1=1");
        if (!ObjectUtils.isEmpty(reqVO.getTransportNos())) {
            stringBuilder.append("&tracking_numbers=").append(StringUtils.join(reqVO.getTransportNos(), ","));
        }
        if (!ObjectUtils.isEmpty(reqVO.getOrderNos())) {
            stringBuilder.append("&order_numbers=").append(StringUtils.join(reqVO.getOrderNos(), ","));
        }
        if (!ObjectUtils.isEmpty(reqVO.getStartTime())) {
            stringBuilder.append("&updated_date_min=").append(reqVO.getStartTime().getTime() / 1000);
        }
        if (!ObjectUtils.isEmpty(reqVO.getEndTime())) {
            stringBuilder.append("&updated_date_max=").append(reqVO.getEndTime().getTime() / 1000);
        }

        if (!ObjectUtils.isEmpty(reqVO.getPage())) {
            stringBuilder.append("&pages_amount=").append(reqVO.getPage());
        }
        if (!ObjectUtils.isEmpty(reqVO.getPageSize())) {
            stringBuilder.append("&items_amount=").append(reqVO.getPageSize());
        }

        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.CAI_NIAO.getName())
            .carrier(ForwarderTypeEnum.CAI_NIAO.getName())
            .orderNumbers(orderIds)
            .build();
        try {
            String url = trackingUrlHost + trackingsUrl + "?" + stringBuilder;

            Headers headers = Headers.of(ImmutableMap.of("Tracking-Api-Key", trackingToken));
            String response = httpClient.getDataWithHeader(url, headers, bizLogParam);
            BaseResponseDTO<List<Track51AusPostResponseDTO>> responseVo = JSON.parseObject(response, new TypeReference<BaseResponseDTO<List<Track51AusPostResponseDTO>>>() {
            });
            if (responseVo.getCode() != 200 || CollectionUtils.isEmpty(responseVo.getData())) {
                log.error("query 51track track fail, requestDTO:{}, orderNumbers:{}, response:{}", JSON.toJSONString(reqVO), reqVO.getOrderNos(), JSON.toJSONString(responseVo));
                return new ArrayList<>();
            }
            return responseVo.getData();
        } catch (Exception e) {
            log.error("query 51track track fail, requestDTO:{}, orderNumbers:{}, exception:", JSON.toJSONString(reqVO), reqVO.getOrderNos(), e);
            throw new BizException(ExceptionEnum.THIRD_SYSTEM_ERROR);
        }
    }

    @Override
    @Async
    public void subscribeTrace(List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfoList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(expressDeliveryOrderInfoList)){
            log.warn("单号为空");
            return;
        }
        if (expressDeliveryOrderInfoList.size()>40){
            List<List<ExpressDeliveryOrderInfo>> partition = Lists.partition(expressDeliveryOrderInfoList, 40);
            for (List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfos : partition) {
                request51Tracking(expressDeliveryOrderInfos);
            }
        }else {
            request51Tracking(expressDeliveryOrderInfoList);
        }
        
    }

    private void request51Tracking(List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfos) {
        List<SubscribeDTO> subscribeDTOList = expressDeliveryOrderInfos.stream()
                .filter(t->Objects.nonNull(courierMap.get(t.getCourierCode()))).map(t ->{
                    SubscribeDTO subscribeDTO = new SubscribeDTO().setTrackingNumber(t.getTransportLabelNo());
                    if (t.getCourierCode().equals("gls-es")){
                        // 设置postCode
                        subscribeDTO.setTrackingPostalCode(t.getTrackingPostalCode());
                    }
                    String courierCode = courierMap.get(t.getCourierCode());
                    subscribeDTO.setCourierCode(courierCode);
                    return subscribeDTO;
                })
                .collect(Collectors.toList());
        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
                .orderNumbers(expressDeliveryOrderInfos.stream().map(ExpressDeliveryOrderInfo::getTransportNo).collect(Collectors.toList()))
                .build();
        try {
            String url = trackingUrlHost + subscribeTraceUrl;
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Tracking-Api-Key",trackingToken);
            String response = httpClient.postJsonData(url, JSON.toJSONString(subscribeDTOList), headerMap, bizLogParam);
            SpecialResponseVO specialResponseVO = JSON.parseObject(response, SpecialResponseVO.class);
            Integer code = Optional.ofNullable(specialResponseVO).map(SpecialResponseVO::getMeta).map(BaseResponseDTO::getCode).orElse(-1);
            log.info("51轨迹订阅的请求参数为{},响应参数为{}",subscribeDTOList,response);
            if (code!=200) {
                log.error("subscribeDTO track fail, requestDTO:{}, response:{}", subscribeDTOList, response);
            }
        }catch (Exception e){
            log.error("51轨迹订阅失败,需要补数据 requestDTO:{}", subscribeDTOList,e);
        }
    }
}
