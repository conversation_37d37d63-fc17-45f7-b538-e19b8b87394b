package com.qudian.idle.bifrost.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.bifrost.infrastructure.repository.database.mapper.ExpressOrderTrackMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderTrackPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 快递订单轨迹 Repository
 * @date 2025-08-29
 */
@Repository
@Slf4j
public class ExpressOrderTrackRepository extends ServiceImpl<ExpressOrderTrackMapper, ExpressOrderTrackPO> {

    /**
     * 以运单查询，一条最新的记录
     *
     * @param waybillNo
     * @return {@link ExpressOrderTrackPO }
     */
    public Map<String /* waybillNo */, ExpressOrderTrackPO> mapByWaybillNoLatest(Set<String> waybillNo) {
        return baseMapper.listByWaybillNoLatest(waybillNo)
                .stream()
                .collect(Collectors.toMap(ExpressOrderTrackPO::getWaybillNo, Function.identity()));
    }

    public List<ExpressOrderTrackPO> selectByWaybillNo(String waybillNo) {
        LambdaQueryWrapper<ExpressOrderTrackPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpressOrderTrackPO::getWaybillNo, waybillNo);
        queryWrapper.orderByDesc(ExpressOrderTrackPO::getTrackTime);
        return this.list(queryWrapper);
    }
}
