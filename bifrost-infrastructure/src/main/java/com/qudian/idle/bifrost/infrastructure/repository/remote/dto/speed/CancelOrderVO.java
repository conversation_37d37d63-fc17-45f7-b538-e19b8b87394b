package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelOrderVO extends BaseRespDTO implements Serializable {

    /**
     * 单号
     */
    @JSONField(name = "reference_no")
    private String referenceNo;

    /**
     * 单号类型
     */
    private String type;
}
