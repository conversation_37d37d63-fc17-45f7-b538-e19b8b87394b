package com.qudian.idle.bifrost.infrastructure.repository.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>文件名称:com.qudian.lme.driver.business.producer</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">kangjun</a>
 * @version 1.0
 * @since 2022/11/10 10:16 上午
 */
@Component
@Slf4j
public class MqProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producer.topic}")
    private String bifrostTopic;


    /**
     * 发送外部消息
     * @param msgBody
     * @param tag
     * @param <T>
     */
    public <T> void sendEntityMsg(T msgBody, String tag, Object keys) {
        Map<String, Object> map = new HashMap<>();
        map.put("KEYS", keys);
        log.info("[MQ_producer]sends pre-preparation. tag:{}, msg:{}", tag, JSONObject.toJSONString(msgBody));
        SendResult sendResult = rocketMQTemplate.syncSend(bifrostTopic + ":" + tag
                , rocketMQTemplate.getMessageConverter().toMessage(MessageBuilder.withPayload(JSON.toJSONString(msgBody)).setHeader("keys", map.get("keys")).setHeader("tag", tag).build(), new MessageHeaders(map)));
        log.info("[MQ_producer]sends successful msg. result is: {}", JSONObject.toJSONString(sendResult));
    }
    public <T> void formulateTopicWhenSendMsg(T msgBody, String tag, Object keys,String topic) {
        Map<String, Object> map = new HashMap<>();
        map.put("KEYS", keys);
        log.info("[MQ_producer]sends pre-preparation. tag:{}, msg:{}", tag, msgBody);
        SendResult sendResult = rocketMQTemplate.syncSend(topic + ":" + tag
                , rocketMQTemplate.getMessageConverter().toMessage(MessageBuilder.withPayload(JSON.toJSONString(msgBody)).setHeader("keys", map.get("keys")).setHeader("tag", tag).build(), new MessageHeaders(map)));
        log.info("[MQ_producer]sends successful msg. result is: {}", sendResult);
    }


}
