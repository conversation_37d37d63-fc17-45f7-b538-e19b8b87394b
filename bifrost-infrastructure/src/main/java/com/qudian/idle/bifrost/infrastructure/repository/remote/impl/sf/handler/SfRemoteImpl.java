//package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONException;
//import com.qudian.idle.bifrost.common.exception.BizException;
//import com.qudian.idle.bifrost.common.utils.HttpClient;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.ExpressRemote;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderRequestDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsRequestDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsResponseDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.SfBaseResponseDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.factory.SfApiHandlerFactory;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.strategy.SfSignGenerator;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.FormBody;
//import okhttp3.Request;
//import okhttp3.RequestBody;
//import okhttp3.Response;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.util.UUID;
//
///**
// * <AUTHOR> Huang
// * @date 2025/8/26
// */
//@Slf4j
//@Component
//public abstract class SfRemoteImpl implements ExpressRemote {
//    @Resource
//    private HttpClient httpClient;
//    @Value("${express.sf.partnerID:YJP2E5E5}")
//    private String partnerID;
//    @Value("${express.sf.createOrder.serviceCode:EXP_RECE_CREATE_ORDER}")
//    private String createOrderServiceCode;
//
//    @Value("${express.sf.printWaybills.serviceCode:COM_RECE_CLOUD_PRINT_WAYBILLS}")
//    private String printWaybillsServiceCode;
//
//    @Value("${express.sf.printWaybills.templateCode:fm_150_standard_YJP2E5E5}")
//    private String printWaybillsTemplateCode;
//    @Value("${express.sf.secret:YlG8QAHKVCvCxF8tujqn4g4Ala1jE20V}")
//    private String secret;
//    @Value("${express.sf.baseUrl:https://sfapi-sbox.sf-express.com/std/service}")
//    private String baseUrl;
//
//    public CreateOrderResponseDTO createOrder(CreateOrderRequestDTO createOrderRequestDTO) {
//        // 构建请求体
//        String timestamp = String.valueOf(System.currentTimeMillis());
//        String msgData = JSON.toJSONString(createOrderRequestDTO);
//        String requestID =  UUID.randomUUID().toString().replace("-", "");
//        log.info("op=请求顺丰创建订单接口，requestID={},参数msgData={}", requestID,msgData);
//        String msgDigest = SfSignGenerator.generateSign(msgData, timestamp, secret);
//        RequestBody formBody = new FormBody.Builder()
//                .add("partnerID", partnerID)
//                .add("requestID", "QDIDLE_" + requestID)
//                .add("serviceCode", createOrderServiceCode)
//                .add("timestamp", timestamp)
//                .add("msgDigest", msgDigest)
//                .add("msgData", msgData)
//                .build();
//
//        // 构建请求
//        Request request = new Request.Builder()
//                .url(baseUrl)
//                .post(formBody)
//                .addHeader("Content-Type", "application/x-www-form-urlencoded")
//                .build();
//
//        // 执行请求（使用try-with-resources自动关闭Response）
//        try (Response response = httpClient.getOkHttpClient().newCall(request).execute()) {
//            // 8. 读取响应体
//            String responseBody = response.body() != null ? response.body().string() : "";
//            log.info("[op=createOrder]顺丰创建订单响应, requestID={}, code={}, responseBody={}", requestID, response.code(), responseBody);
//
//            // 处理响应结果
//            if (response.isSuccessful()) {
//                return handleSuccessResponse(responseBody, requestID);
//            } else {
//                // 非200状态码抛出异常
//                throw new BizException("调用失败, requestID=" + requestID + ", code=" + response.code() + ", message=" + responseBody);
//            }
//        } catch (BizException e) {
//            // 业务异常直接抛出（上层统一处理）
//            log.error("[op=createOrder]业务异常, requestID={}", requestID, e);
//            throw e;
//        } catch (IOException e) {
//            log.error("[op=createOrder]网络请求异常, requestID={}", requestID, e);
//            throw new BizException("请求接口网络异常, requestID=" + requestID);
//        } catch (Exception e) {
//            // 其他未知异常
//            log.error("[op=createOrder]系统异常, requestID={}", requestID, e);
//            throw new BizException("请求接口系统异常, requestID=" + requestID);
//        }
//    }
//
//    @Override
//    public PrintWaybillsResponseDTO printWaybills(PrintWaybillsRequestDTO printWaybillsRequestDTO) {
//        SfApiHandlerFactory
//    }
//
//
//
//
//    /**
//     * 处理成功响应（200状态码）
//     */
//    private PrintWaybillsResponseDTO handleSuccessResponse2(String responseBody, String requestID) {
//        try {
//            // 解析基础响应
//            SfBaseResponseDTO sfBaseResponseDTO = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
//            if (sfBaseResponseDTO == null || StrUtil.isBlank(sfBaseResponseDTO.getApiResultData())) {
//                throw new BizException("顺丰响应格式异常, 无业务数据, requestID=" + requestID);
//            }
//
//            // 解析泛型业务数据
//            PrintWaybillsResponseDTO resultData = JSON.parseObject(sfBaseResponseDTO.getApiResultData(), PrintWaybillsResponseDTO.class);
//            resultData.setRequestId(requestID);
//            if (resultData == null) {
//                throw new RuntimeException("系统异常：接口返回数据解析失败, requestID=" + requestID);
//            }
//
//            // 处理业务成功/失败
//            if (resultData.isSuccess()) {
//                PrintWaybillsResponseDTO.PrintWaybillsResponse data = resultData.getObj();
//                if (data == null) {
//                    throw new RuntimeException("顺丰返回业务数据为空, requestID=" + requestID);
//                }
//                return resultData;
//            } else {
//                throw new RuntimeException("系统异常：接口返回失败 (requestID:" + resultData.getRequestId()+")");
//            }
//
//        } catch (JSONException e) {
//            throw new RuntimeException("顺丰响应JSON解析失败, requestID=" + requestID);
//        }
//    }
//
//    /**
//     * 处理成功响应（200状态码）
//     */
//    private CreateOrderResponseDTO handleSuccessResponse(String responseBody, String requestID) {
//        try {
//            // 解析基础响应
//            SfBaseResponseDTO sfBaseResponseDTO = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
//            if (sfBaseResponseDTO == null || StrUtil.isBlank(sfBaseResponseDTO.getApiResultData())) {
//                throw new BizException("顺丰响应格式异常, 无业务数据, requestID=" + requestID);
//            }
//
//            // 解析泛型业务数据
//            CreateOrderResponseDTO resultData = JSON.parseObject(sfBaseResponseDTO.getApiResultData(), CreateOrderResponseDTO.class);
//            resultData.setRequestId(requestID);
//            if (resultData == null) {
//                throw new RuntimeException("系统异常：接口返回数据解析失败, requestID=" + requestID);
//            }
//
//            // 处理业务成功/失败
//            if (resultData.isSuccess()) {
//                CreateOrderResponseDTO.CreateOrderResponse data = resultData.getMsgData();
//                if (data == null) {
//                    throw new RuntimeException("顺丰返回业务数据为空, requestID=" + requestID);
//                }
//                String waybillNo = data.getWaybillNoInfoList().get(0).getWaybillNo();
//                log.info("[op=createOrder]请求成功, requestID={}, 运单号={}", requestID, waybillNo);
//                return resultData;
//            } else {
//                throw new RuntimeException(resultData.getErrorCode() +":"+ resultData.getErrorMsg() + "(requestID:" + requestID+")");
//            }
//
//        } catch (JSONException e) {
//            throw new RuntimeException("顺丰响应JSON解析失败, requestID=" + requestID);
//        }
//    }
//
//}
