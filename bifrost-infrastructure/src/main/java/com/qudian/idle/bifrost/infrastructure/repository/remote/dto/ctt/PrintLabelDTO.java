package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ctt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PrintLabelDTO {
    @JSONField(name = "label_type_code")
    private String labelTypeCode;
    @JSONField(name = "model_type_code")
    private String modelTypeCode;
    @JSONField(name = "label_offset")
    private Integer labelOffset;
}
