package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderVO extends BaseRespDTO implements Serializable {
    @JSONField(name = "agent_number")
    private String agentNumber;

    @J<PERSON>NField(name = "order_code")
    private String orderCode;

    @JSONField(name = "reference_no")
    private String referenceNo;

    @JSONField(name = "shipping_method_no")
    private String shippingMethodNo;

    @JSONField(name = "track_status")
    private Integer trackStatus;

    @JSONField(name = "tracking_number_list")
    private Object trackingNumberList;

    @J<PERSON><PERSON>ield(name = "labelBase64")
    private String labelBase64;
}
