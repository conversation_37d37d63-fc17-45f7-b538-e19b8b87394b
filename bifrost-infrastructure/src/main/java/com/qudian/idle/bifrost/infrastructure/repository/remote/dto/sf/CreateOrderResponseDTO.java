package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CreateOrderResponseDTO implements Serializable {
    private boolean success;
    private String errorCode;
    private String errorMsg;

    private CreateOrderResponse msgData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class CreateOrderResponse {
        private String orderId;
        private String originCode;
        private String destCode;
        private int filterResult;
        private String remark;
        private String url;
        private String paymentLink;
        private String isUpstairs;
        private String isSpecialWarehouseService;
        private String mappingMark;
        private String agentMailno;
        private String returnExtraInfoList;
        private List<WaybillNoInfoList> waybillNoInfoList;
        private List<RouteLabelInfo> routeLabelInfo;
        private String contactInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class WaybillNoInfoList {
        private int waybillType;
        private String waybillNo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class RouteLabelInfo {
        private String waybillNo;
        private String sourceTransferCode;
        private String sourceCityCode;
        private String sourceDeptCode;
        private String sourceTeamCode;
        private String destCityCode;
        private String destDeptCode;
        private String destDeptCodeMapping;
        private String destTeamCode;
        private String destTeamCodeMapping;
        private String destTransferCode;
        private String destRouteLabel;
        private String proName;
        private String cargoTypeCode;
        private String limitTypeCode;
        private String expressTypeCode;
        private String codingMapping;
        private String codingMappingOut;
        private String xbFlag;
        private String printFlag;
        private String twoDimensionCode;
        private String proCode;
        private String printIcon;
        private String abFlag;
        private String destPortCode;
        private String destCountry;
        private String destPostCode;
        private String goodsValueTotal;
        private String currencySymbol;
        private String cusBatch;
        private String goodsNumber;
        private String errMsg;
        private String checkCode;
        private String proIcon;
        private String fileIcon;
        private String fbaIcon;
        private String icsmIcon;
        private String destGisDeptCode;
        private String newIcon;
    }

}
