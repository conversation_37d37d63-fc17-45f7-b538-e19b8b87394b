package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Getter;

import javax.xml.bind.annotation.*;

@XmlRootElement(name = "AnulaResponse", namespace = "http://www.asmred.com/")
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class CancelResponse {
    @XmlElement(name = "AnulaResult",namespace = "http://www.asmred.com/")
    private CancelResult cancelResult;
    @Getter
    public static class CancelResult {
        @XmlElement(name = "Servicios",namespace = "")
        private Servicios servicios;
    }

    @Getter
    public static class Servicios {
        @XmlElement(name = "Envio")
        private Envio envio;
    }

    @Getter
    public static class Envio {
        @XmlElement(name = "Resultado")
        private Resultado resultado;
    }
    @Getter
    public static class Resultado {
        @XmlAttribute(name = "return")
        private Integer returnCode;
        @XmlValue
        private String value;
    }
}
