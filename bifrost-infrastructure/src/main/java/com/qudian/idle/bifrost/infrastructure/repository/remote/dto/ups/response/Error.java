package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.response;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * {@inheritDoc} ups错误返回
 *
 * <AUTHOR>
 * @since 2023/9/7
 **/
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class Error {
    @XmlElement(name = "Number")
    private String number;

    @XmlElement(name = "Description")
    private String description;

}
