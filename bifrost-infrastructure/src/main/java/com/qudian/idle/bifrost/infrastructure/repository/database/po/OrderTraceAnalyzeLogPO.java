package com.qudian.idle.bifrost.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 运单物流轨迹解析表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bifrost_order_trace_analyze_log")
public class OrderTraceAnalyzeLogPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 转运商类型
     */
    private String forwarderType;
    /**
     * 运单id
     */
    private String orderNumber;

    /**
     * 本次解析时的转运商运单状态
     */
    private String orderStatus;

    /**
     * 处理状态 0 处理中 1 处理完成
     */
    private Integer status;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 本次解析时间utc格式
     */
    private String analyzeTime;

    /**
     * 本次解析增量事件
     */
    private String events;

    /**
     * 是否删除 1已删除 0 未删除
     */
    private Integer deleteFlag;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;


}
