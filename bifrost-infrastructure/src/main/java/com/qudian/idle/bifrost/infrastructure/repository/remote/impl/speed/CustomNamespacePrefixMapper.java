package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.speed;

import com.sun.xml.bind.marshaller.NamespacePrefixMapper;

public class CustomNamespacePrefixMapper extends NamespacePrefixMapper {
    @Override
    public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
        if ("http://www.example.org/Ec/".equals(namespaceUri)) {
            return "ns1"; // 指定您希望的命名空间前缀
        }
        return suggestion;
    }
}
