package com.qudian.idle.bifrost.infrastructure.annotation;

import org.apache.dubbo.config.annotation.Reference;

import java.lang.annotation.*;

/**
 * Dubbo服务引用标识
 * <AUTHOR>
 * @date 2022/06/21
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Reference(check = false, version = "${dubbo.refrence.defalut.verion}")
public @interface DubboReference {
}
