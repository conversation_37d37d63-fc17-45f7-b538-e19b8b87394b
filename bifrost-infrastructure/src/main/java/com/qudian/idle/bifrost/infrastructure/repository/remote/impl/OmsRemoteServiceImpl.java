package com.qudian.idle.bifrost.infrastructure.repository.remote.impl;

import com.alibaba.fastjson.JSON;
import com.qudian.channelms.api.dto.ChannelInfoReqDTO;
import com.qudian.channelms.api.dto.ChannelInfoRespDTO;
import com.qudian.channelms.api.dto.base.CommonRespDTO;
import com.qudian.channelms.api.service.ChannelInfoFacede;
import com.qudian.idle.bifrost.infrastructure.repository.remote.OmsRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OmsRemoteServiceImpl implements OmsRemoteService {
    @Reference(version = "1.0.1")
    private ChannelInfoFacede channelInfoFacede;
    @Override
    public String listCarrierByChannel(String channel) {
        try {
            if (StringUtils.isBlank(channel)){
                return null;
            }
            ChannelInfoReqDTO channelInfoReqDTO = new ChannelInfoReqDTO();
            channelInfoReqDTO.setChannelName(channel);
            CommonRespDTO<List<ChannelInfoRespDTO>> listSettleRespDTO = channelInfoFacede.listChannelInfo(channelInfoReqDTO);
            log.info("请求参数为{},oms返回的返回为{}",channel,JSON.toJSON(listSettleRespDTO));
            if (listSettleRespDTO.isSuccess()){
                List<String> forwarderConfigs = listSettleRespDTO.getData().get(0).getForwarderConfigs();
                if (CollectionUtils.isNotEmpty(forwarderConfigs)){
                    return forwarderConfigs.get(0);
                }
            }else {
                log.warn("请求的参数为{},响应的参数为{}",channel, JSON.toJSON(listSettleRespDTO));
            }
        }catch (Exception e){
            log.error("请求oms获取对应的渠道失败",e);
        }
        return null;
    }
}
