package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.speed;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.xml.bind.annotation.XmlType;

@Data
@SuperBuilder
@XmlType(propOrder = {"paramsJson", "appToken", "appKey", "service"})
@NoArgsConstructor
@AllArgsConstructor
public class BaseReqDTO {
    /**
     * api账号
     */
    private String appToken;

    /**
     * api密码
     */
    private String appKey;

    /**
     * 接口名
     */
    private String service;


    /**
     * 请求参数
     */
    private String paramsJson;
}
