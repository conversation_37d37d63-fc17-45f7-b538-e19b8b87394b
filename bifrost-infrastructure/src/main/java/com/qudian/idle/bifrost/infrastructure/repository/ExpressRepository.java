//package com.qudian.idle.bifrost.infrastructure.repository;
//
//import com.alibaba.fastjson.JSON;
//import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
//import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
//import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
//import com.qudian.idle.bifrost.api.vo.response.express.BatchPrintRespVO;
//import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
//import com.qudian.idle.bifrost.api.vo.response.tool.UploadFileRespVO;
//import com.qudian.idle.bifrost.common.utils.HttpClient;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.ToolSrvRemote;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderRequestDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsRequestDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsResponseDTO;
//import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.factory.SfApiHandlerFactory;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
///**
// * <AUTHOR> Huang
// * @date 2025/8/27
// */
//@Component
//@Slf4j
//public class ExpressRepository {
//
//    @Resource
//    private HttpClient httpClient;
//    @Resource
//    private ToolSrvRemote toolSrvRemote;
//
//    @Resource
//    private SfApiHandlerFactory sfApiHandlerFactory;
//
//    public CreateExpressRespVO create(CreateExpressReqVO createExpressReqVO) {
//        log.info("op=start_SFRemoteServiceImpl.create, createExpressReqVO={}", createExpressReqVO);
//        CreateOrderRequestDTO createOrderRequestDTO = CreateOrderRequestDTO.builder().build();
//        createOrderRequestDTO.setLanguage("zh-CN");
//        //Todo: @hfq 确认订单 运单号
//        createOrderRequestDTO.setOrderId(createExpressReqVO.getOrderNo() + "_" + System.currentTimeMillis());
//        createOrderRequestDTO.setSendStartTm(createExpressReqVO.getSendStartTm());
//
//        //货物
//        String cargoName = createExpressReqVO.getCargoDetails().get(0).getName();
//        createOrderRequestDTO.setCargoDesc(cargoName);
//        List<CreateOrderRequestDTO.CargoDetail> cargoDetails = Arrays.asList(CreateOrderRequestDTO.CargoDetail.builder().name(cargoName).build());
//        createOrderRequestDTO.setCargoDetails(cargoDetails);
//
//        //设置发件人
//        CreateExpressReqVO.ContactInfo sendContactVO = createExpressReqVO.getSendContactInfo();
//        CreateOrderRequestDTO.ContactInfo sendContactInfo = CreateOrderRequestDTO.ContactInfo.builder()
//                .address(sendContactVO.getAddress()).city(sendContactVO.getCity()).contact(sendContactVO.getContact())
//                .contactType(1).mobile(sendContactVO.getMobile()).province(sendContactVO.getProvince()).country("CN").build();
//
//        //设置收货人
//        CreateExpressReqVO.ContactInfo destContactVO = createExpressReqVO.getDestContactInfo();
//        CreateOrderRequestDTO.ContactInfo receiveContactInfo = CreateOrderRequestDTO.ContactInfo.builder()
//                .address(destContactVO.getAddress()).city(destContactVO.getCity()).contact(destContactVO.getContact())
//                .contactType(2).mobile(destContactVO.getMobile()).province(destContactVO.getProvince()).country("CN").build();
//
//        List<CreateOrderRequestDTO.ContactInfo> contactInfoList = new ArrayList<>();
//        contactInfoList.add(sendContactInfo);
//        contactInfoList.add(receiveContactInfo);
//        createOrderRequestDTO.setContactInfoList(contactInfoList);
//
//
//        String waybillNo = "";
//        CreateOrderResponseDTO createOrderResponseDTO  = (CreateOrderResponseDTO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.CREATE_ORDER).execute(createOrderRequestDTO);
//        try {
//            waybillNo = createOrderResponseDTO.getMsgData().getWaybillNoInfoList().get(0).getWaybillNo();
//        } catch (Exception e) {
//            throw new RuntimeException("获取运单号出错");
//        }
//
//        return CreateExpressRespVO.builder().orderNo(createExpressReqVO.getOrderNo()).waybillNo(waybillNo).requestId(createOrderResponseDTO.getRequestId()).build();
//    }
//
//    public BatchPrintRespVO batchPrintVerify(BatchPrintReqVO batchPrintReqVO) {
//        List<PrintWaybillsRequestDTO.Document> documentList = new ArrayList<>();
//        batchPrintReqVO.getBatchRequest().forEach(t->{
//            documentList.add(PrintWaybillsRequestDTO.Document.builder().masterWaybillNo(t.getWaybillNo()).build());
//        });
//        PrintWaybillsRequestDTO printWaybillsRequestDTO = PrintWaybillsRequestDTO.builder().documents(documentList).build();
//
//        PrintWaybillsResponseDTO printWaybillsResponseDTO = (PrintWaybillsResponseDTO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.BATCH_PRINT_WAYBILLS).execute(printWaybillsRequestDTO);
//
//        List<BatchPrintRespVO.BatchPrintResp> batchResponse = new ArrayList<>();
//
//        printWaybillsResponseDTO.getObj().getFiles().forEach(file -> {
//            String token = file.getToken();
//            String fileUrl = file.getUrl();
//            try {
//                //下载文件
//                byte[] bytes = httpClient.download(fileUrl, token);
//                UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(bytes, file.getWaybillNo() + ".pdf");
//                log.info("op=start_上传 oss 返回={}", JSON.toJSON(uploadFileRespVO));
//                String ossUrl =uploadFileRespVO.getData().getSigned_url();
//                  batchResponse.add(BatchPrintRespVO.BatchPrintResp.builder().pdfObjectKey(uploadFileRespVO.getData().getObject_key())
//                          .waybillNo(file.getWaybillNo()).pdfUrl(ossUrl).build());
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        return BatchPrintRespVO.builder().batchResponse(batchResponse).build();
//    }
//
//
//
//}
