package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace;

import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRequestDTO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SearchRoutesRespDTO extends BaseResponseVO {
    private boolean success;
    private String errorCode;
    private String errorMsg;
    private SearchRoutesDataResps msgData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SearchRoutesDataResps {
        private List<SearchRoutesDataResp> routeResps;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SearchRoutesDataResp {
        private String mailno;
        private List<SfSearchRoutesElemResp> routes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SfSearchRoutesElemResp {
        private String acceptTime;
        private String acceptAddress;
        private String remark;
        private String opCode;
        private String firstStatusCode;
        private String firstStatusName;
        private String secondaryStatusCode;
        private String secondaryStatusName;
    }
}
