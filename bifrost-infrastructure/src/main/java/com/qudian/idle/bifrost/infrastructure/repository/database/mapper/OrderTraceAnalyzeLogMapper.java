package com.qudian.idle.bifrost.infrastructure.repository.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTraceAnalyzeLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 运单物流轨迹解析表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Mapper
public interface OrderTraceAnalyzeLogMapper extends BaseMapper<OrderTraceAnalyzeLogPO> {

    @Select("<script>" + "SELECT *  FROM bifrost_order_trace_analyze_log WHERE order_number IN" +
        "        <foreach collection='list' item='item'  open='(' separator=',' close=')'>\n" +
        "            #{item}" +
        "        </foreach>" + "AND delete_flag = 0 AND STATUS = 1 AND carrier = #{carrier}" + "</script>")
    List<OrderTraceAnalyzeLogPO> selectByOrderNumbersAndCarrier(@Param("list") List<String> orderNumbers, @Param("carrier") String carrier);


    @Select("SELECT * FROM bifrost_order_trace_analyze_log WHERE order_number = #{orderNumber} AND delete_flag = 0 AND STATUS = 1 order by id desc limit 1")
    OrderTraceAnalyzeLogPO selectLastOneByOrderNumber(@Param("orderNumber") String orderNumber);

}
