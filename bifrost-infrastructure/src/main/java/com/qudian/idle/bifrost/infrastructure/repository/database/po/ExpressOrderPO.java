package com.qudian.idle.bifrost.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.bifrost.api.enums.ExpressOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.Date;

import java.io.Serializable;

/**
 * 快递订单 PO
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain=true)
@TableName("`express_order`")
public class ExpressOrderPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 内部订单号
     */
    private String orderNo;
    private String source;
    private String requestId;
    /**
     * 寄件方手机号
     */
    private String senderPhone;
    /**
     * 收件方手机号
     */
    private String receiverPhone;
    /**
     * 快递运单号
     */
    private String waybillNo;
    /**
     * 快递对接订单号，唯一
     */
    private String expressOrderNo;
    /**
     * 平台：1-顺丰
     */
    private String platform;
    /**
     * 下单详细信息
     */
    private String expressJson;
    /**
     * @see ExpressOrderStatusEnum
     * 订单状态：0-初始化 1-已下单 2-已取消 3-已揽收 4-运输中 5-已派送 6-已签收 7-异常
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createdId;
    private String createdName;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新人
     */
    private String updatedId;
    /**
     * 更新时间
     */
    private Date updatedTime;
    /**
     * 软删除标识，0表示未删除，1表示已删除
     */
    @TableLogic
    private Integer deleteFlag;

}
