package com.qudian.idle.bifrost.infrastructure.repository.remote;

import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;

public interface SendSpeedRemoteService {
    void cancelTransport(CancelTransportReqVO reqVO);

    PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);
}
