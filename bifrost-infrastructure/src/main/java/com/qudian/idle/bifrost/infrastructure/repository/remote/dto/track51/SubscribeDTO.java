package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SubscribeDTO {
  @JSONField(name = "tracking_number")
  private String trackingNumber;
  @JSONField(name = "courier_code")
  private String courierCode;
  @JSONField(name = "tracking_postal_code")
  private String trackingPostalCode;
}
