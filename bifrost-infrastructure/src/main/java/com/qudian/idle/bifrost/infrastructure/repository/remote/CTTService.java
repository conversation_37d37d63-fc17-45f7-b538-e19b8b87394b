package com.qudian.idle.bifrost.infrastructure.repository.remote;

import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;

public interface CTTService {
    void cancelTransport(CancelTransportReqVO reqVO);

    PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);

    TransportTrackRespVO shippingStatus(ShippingStatusReqDTO reqVO);
    
    String getToken();
}
