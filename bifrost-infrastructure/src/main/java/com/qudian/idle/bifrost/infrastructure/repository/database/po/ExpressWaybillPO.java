package com.qudian.idle.bifrost.infrastructure.repository.database.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.Date;

import java.io.Serializable;

/**
 * 面单信息 PO
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain=true)
@TableName("`express_waybill`")
public class ExpressWaybillPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 面单文件类型
     */
    private String fileType;
    /**
     * 面单pdf key
     */
    private String waybillObjectKey;
    /**
     * 面单pdf URL
     */
    private String waybillUrl;
    private String requestId;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updatedTime;
    /**
     * 软删除标识，0表示未删除，1表示已删除
     */
    @TableLogic
    private Integer deleteFlag;

}
