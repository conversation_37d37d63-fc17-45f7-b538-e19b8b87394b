package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "GrabaServiciosResponse", namespace = "http://www.asmred.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class GrabaServiciosResponseVO {
    @XmlElement(name = "GrabaServiciosResult")
    private GrabaServiciosResult grabaServiciosResult;
    @XmlAccessorType(XmlAccessType.FIELD)
    @Data
    public  static class GrabaServiciosResult{
        @XmlElement(name = "Servic<PERSON>")
        private Servicios servicios;
    }
}

