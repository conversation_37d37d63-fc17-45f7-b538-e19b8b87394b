package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.idle.bifrost.api.vo.BaseRequestVO;
import com.qudian.idle.bifrost.api.vo.BaseResponseVO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.utils.HttpClient;
import com.qudian.idle.bifrost.infrastructure.repository.ApiInvokeLogRepository;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ApiInvokeLogPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.strategy.SignStrategy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Slf4j
@Component
public abstract class AbstractSfApiTemplate<V extends BaseRequestVO, T, R extends BaseResponseVO> {
    @Resource
    private HttpClient httpClient;
    @Value("${express.sf.partnerID:YJP2E5E5}")
    private String partnerID;
    @Value("${express.sf.secret:YlG8QAHKVCvCxF8tujqn4g4Ala1jE20V}")
    private String secret;
    @Value("${express.sf.baseUrl:https://sfapi-sbox.sf-express.com/std/service}")
    private String baseUrl;
    @Resource
    protected SignStrategy signStrategy;
    @Resource
    private ApiInvokeLogRepository apiInvokeLogRepository;
    @Resource(name = "logSaveThreadPool")
    private ExecutorService logSaveThreadPool;


    // 模板方法：定义通用流程（final修饰，禁止子类修改）
    public final R execute(V reqVO) {
        // 记录开始时间，用于计算耗时
        long startTime = System.currentTimeMillis();
        String requestID = generateRequestID();
        if (StrUtil.isBlank(reqVO.getRequestId())) {
            reqVO.setRequestId(requestID);
        }
        // 创建日志对象
        ApiInvokeLogPO apiLog = createApiInvokeLog(reqVO);
        T requestDTO = null;
        try {
            requestDTO = convert(reqVO);
            // 记录请求参数
            apiLog.setRequestParam(JSON.toJSONString(requestDTO));


            String timestamp = generateTimestamp();

            // 1. 构建业务参数JSON（抽象方法，子类实现）
            String msgData = buildMsgData(requestDTO);
            log.info("[op={}]请求参数, requestID={}, msgData={}", getServiceCode(), requestID, msgData);

            // 2. 生成签名（通用逻辑）
            String msgDigest = signStrategy.generateSign(msgData, timestamp, secret);

            // 3. 构建请求体（通用逻辑）
            RequestBody formBody = buildFormBody(requestID, msgData, timestamp, msgDigest);

            // 4. 构建HTTP请求（通用逻辑）
            Request request = buildRequest(formBody);

            // 5. 发送请求并处理响应（通用逻辑）
            String responseBody = sendRequest(reqVO, request);
            apiLog.setResponseResult(responseBody);

            R response = handleResponse(reqVO, responseBody);
            // 更新日志为成功状态
            apiLog.setStatus(1); // 1-成功
            return response;
        } catch (BizException e) {
            log.error("[op={}]业务异常, requestID={}", getServiceCode(), requestID, e);
            apiLog.setStatus(2); // 2-失败
            apiLog.setErrorMsg(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[op={}]系统异常, requestID={}", getServiceCode(), requestID, e);
            apiLog.setStatus(2); // 2-失败
            apiLog.setErrorMsg(e.getMessage());
            throw new BizException(getServiceCode() + "接口调用失败, requestID=" + requestID);
        } finally {
            // 计算耗时
            long costTime = System.currentTimeMillis() - startTime;
            apiLog.setCostTime((int) TimeUnit.MILLISECONDS.toMillis(costTime));

            // 判断是否需要保存日志，由子类决定
            if (shouldSaveLog()) {
                saveLog(apiLog, costTime);
            }
        }
    }

    private void saveLog(ApiInvokeLogPO apiLog, long costTime) {
        logSaveThreadPool.submit(() -> {
            try {
                apiInvokeLogRepository.insert(apiLog);
                log.info("[op={}]接口调用日志已保存, requestID={}, 耗时={}ms", getServiceCode(), apiLog.getRequestId(), costTime);
            } catch (Exception e) {
                log.error("[op={}]保存接口调用日志失败, requestID={}", getServiceCode(), apiLog.getRequestId(), e);
            }
        });
    }

    /**
     * 是否保存日志，子类可以重写此方法决定是否保存
     *
     * @return true-保存，false-不保存
     */
    protected boolean shouldSaveLog() {
        // 默认保存所有日志，子类可根据需要修改
        return true;
    }

    /**
     * 创建API调用日志对象，子类可以重写以设置更多默认信息
     */
    protected ApiInvokeLogPO createApiInvokeLog(V reqVO) {
        ApiInvokeLogPO log = new ApiInvokeLogPO();
        log.setPlatform(1); // 默认1-顺丰，子类可根据实际情况修改
        log.setApiName(getServiceCode());
        log.setApiDesc(support().getDesc());
        log.setRequestId(reqVO.getRequestId());
        log.setInvokeTime(new Date());
        log.setStatus(0); // 0-处理中
        // 从请求参数中获取订单号（假设reqVO有getExpressOrderNo方法）
        log.setObjectId(getObjectId(reqVO));
        return log;
    }

    /**
     * 日志表关联对象 Id，子类可以重写进行 id 关联
     */
    protected String getObjectId(V reqVO) {
        return "";
    }

    // 生成唯一请求ID（通用）
    protected String generateRequestID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    // 生成时间戳（通用，可覆写）
    protected String generateTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    // 构建表单请求体（通用）
    protected RequestBody buildFormBody(String requestID, String msgData, String timestamp, String msgDigest) {
        return new FormBody.Builder()
                .add("partnerID", partnerID)
                .add("requestID", "QDIDLE_" + requestID)
                .add("serviceCode", getServiceCode()) // 服务代码由子类提供
                .add("timestamp", timestamp)
                .add("msgDigest", msgDigest)
                .add("msgData", msgData)
                .build();
    }

    // 构建HTTP请求（通用）
    protected Request buildRequest(RequestBody formBody) {
        return new Request.Builder()
                .url(baseUrl)
                .post(formBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
    }

    // 发送请求（通用）
    protected String sendRequest(V reqVO, Request request) throws IOException {
        try (Response response = httpClient.getOkHttpClient().newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            log.info("[op={}]响应, requestID={}, code={}, responseBody={}", getServiceCode(), reqVO.getRequestId(), response.code(), responseBody);

            if (response.isSuccessful()) {
                return responseBody;
            } else {
                throw new BizException(getServiceCode() + "接口调用失败, requestID=" + reqVO.getRequestId() + ", code=" + response.code() + ", message=" + responseBody);
            }
        }
    }


    //处理响应（通用）
    protected R handleResponse(V reqVO,String responseBody) throws IOException {
        // 解析响应（抽象方法，子类实现）
        R parseResponse = parseResponse(reqVO, responseBody);
        parseResponse.setRequestId(reqVO.getRequestId());
        return parseResponse;
    }

    // 抽象方法：获取服务代码（如EXP_RECE_CREATE_ORDER）
    private String getServiceCode() {
        return support().getServiceCode();
    }

    //抽象方法： 支持的 api（子类实现）
    public abstract SfApiCodeEnum support();

    // 抽象方法：构建业务参数JSON
    protected String buildMsgData(T requestDTO) {
        return JSON.toJSONString(requestDTO);
    }


    // 抽象方法：解析响应为业务对象（子类实现）
    protected abstract R parseResponse(V reqVO, String responseBody);

    // 抽象方法：转换VO为DTO（子类实现）
    protected abstract T convert(V reqVO);
}
