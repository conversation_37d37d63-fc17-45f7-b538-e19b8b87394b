package com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.SfBaseResponseDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesReqDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>文件名称:com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.handler.SearchRoutesHandler</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Slf4j
@Component
public class SearchRoutesHandler extends AbstractSfApiTemplate<SearchRoutesReqVO, SearchRoutesReqDTO, SearchRoutesRespDTO> {
    @Override
    public SfApiCodeEnum support() {
        return SfApiCodeEnum.SEARCH_ROUTES;
    }

    @Override
    protected SearchRoutesRespDTO parseResponse(SearchRoutesReqVO reqVO, String responseBody) {
        String requestId = reqVO.getRequestId();

        // 解析创建订单的响应（仅关注当前接口的响应处理）
        SfBaseResponseDTO baseResponse = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
        if (baseResponse == null || StrUtil.isBlank(baseResponse.getApiResultData())) {
            throw new BizException("查询物流轨迹响应格式异常, requestID=" + requestId);
        }


        SearchRoutesRespDTO respDTO = JSON.parseObject(baseResponse.getApiResultData(), SearchRoutesRespDTO.class);
        if (Objects.isNull(respDTO) || !respDTO.isSuccess()) {
            throw new RuntimeException("查询物流轨迹失败, requestID=" + reqVO.getRequestId());
        }
        return respDTO;
    }

    @Override
    protected SearchRoutesReqDTO convert(SearchRoutesReqVO reqVO) {
        return new SearchRoutesReqDTO()
                .setTrackingType(1)
                .setTrackingNumber(reqVO.getWaybillNo());
    }
}
