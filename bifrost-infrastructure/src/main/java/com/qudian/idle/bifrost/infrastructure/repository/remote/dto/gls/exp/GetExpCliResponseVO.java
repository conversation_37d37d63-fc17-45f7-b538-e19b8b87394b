package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls.exp;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: yangxinye
 * @Date: 2024/4/10
 * @Version: 1.0.0
 **/
@XmlRootElement(namespace = "http://www.asmred.com/", name = "GetExpCliResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
public class GetExpCliResponseVO {

    @XmlElement(name = "GetExpCliResult", namespace = "http://www.asmred.com/")
    private GetExpCliResult result;

}
