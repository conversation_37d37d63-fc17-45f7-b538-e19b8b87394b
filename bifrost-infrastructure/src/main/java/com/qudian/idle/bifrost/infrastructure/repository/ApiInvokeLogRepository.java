package com.qudian.idle.bifrost.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.bifrost.infrastructure.repository.database.mapper.ApiInvokeLogMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ApiInvokeLogPO;
import org.springframework.stereotype.Repository;
import java.util.Optional;


import javax.annotation.Resource;
import java.util.List;


/**
 * @description 接口调用日志 Repository
 * <AUTHOR>
 * @date 2025-08-29
 */
@Repository
public class  ApiInvokeLogRepository{
    @Resource
    private ApiInvokeLogMapper apiInvokeLogMapper;

    public Optional<ApiInvokeLogPO> selectById(Long id) {
        ApiInvokeLogPO apiInvokeLogPO = apiInvokeLogMapper.selectById(id);
        return Optional.ofNullable(apiInvokeLogPO);
    }

    public boolean deleteById(Long id) {
        return SqlHelper.retBool(apiInvokeLogMapper.deleteById(id));
    }

//	public Optional<ApiInvokeLogPO> selectByName(String name) {
//		QueryWrapper<ApiInvokeLogPO> queryWrapper = new QueryWrapper<>();
//		queryWrapper.lambda().eq(ApiInvokeLogPO::getName, name);
//			ApiInvokeLogPO apiInvokeLogPO = apiInvokeLogMapper.selectOne(queryWrapper);
//		return Optional.ofNullable(apiInvokeLogPO);
//	}

    public List<ApiInvokeLogPO> queryList(ApiInvokeLogPO listReqVO) {
        return apiInvokeLogMapper.queryList(listReqVO);
    }

    public boolean insert(ApiInvokeLogPO apiInvokeLogPO) {
        return SqlHelper.retBool(apiInvokeLogMapper.insert(apiInvokeLogPO));
    }

    public boolean update(ApiInvokeLogPO apiInvokeLogPO) {
        return SqlHelper.retBool(apiInvokeLogMapper.updateById(apiInvokeLogPO));
    }

}
