package com.qudian.idle.bifrost.infrastructure.repository.remote;

import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.AusTransPortAccountInfoDTO;

import java.util.List;

public interface CommonBaseInfoService {
    List<String> splitAddressInfo(String address);

    String processCityCode(PushSingleTransportReqVO.RecipientInfo recipientInfo);

    String calculateTotalWeight(PushSingleTransportReqVO pushSingleTransportReqVO);

    AusTransPortAccountInfoDTO getAccountInfoByState(String state);


    /**
     * 根据来源和channel选择渠道
     * @param channel
     * @param origin
     * @return
     */
    String choiceChannel(String channel,String origin,String carrier);

    PrintLabelResponseVO parseBase64PdfAndUpload(String base64PdfString);

}
