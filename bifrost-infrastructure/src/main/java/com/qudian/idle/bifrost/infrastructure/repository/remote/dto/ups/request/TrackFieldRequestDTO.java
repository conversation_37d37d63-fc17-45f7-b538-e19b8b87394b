package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ups.request;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@XmlRootElement(name = "TrackFieldRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class TrackFieldRequestDTO {

    @XmlAttribute(name = "USERID")
    private String userId;

    @XmlAttribute(name = "PASSWORD")
    private String password;

    @XmlElement(name = "Revision")
    private Integer revision;

    @XmlElement(name = "ClientIp")
    private String clientIp;

    @XmlElement(name = "SourceId")
    private String sourceId;

    @XmlElement(name = "TrackID")
    private List<TrackIdDTO> trackIdList;
}
