package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Data
@Accessors(chain = true)
@XmlAccessorType(XmlAccessType.FIELD)
public class CancelDocIn {
    @XmlElement(name = "Servicios", namespace = "http://www.asmred.com/")
    private Servicios servicios;
}
