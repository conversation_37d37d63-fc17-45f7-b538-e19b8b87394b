package com.qudian.idle.bifrost.infrastructure.repository.database.mapper;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/29
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressWaybillPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 面单信息 Mapper
 * @date 2025-08-29
 */
public interface ExpressWaybillMapper extends BaseMapper<ExpressWaybillPO> {

    @Select({"<script>",
            "SELECT * from express_waybill t where 1=1 ",
            "and t.delete_flag=0 ",
//			DATE_FORMAT(t.created_time,'%Y-%m-%d %H:%i:%s') created_time
//			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(name)' > and t.name LIKE CONCAT('%',#{name},'%') </if>",
//          "<if test='status!=null' > and t.status = #{status} </if>",
//			"<if test='createdStartTime != null' > and t.created_time &gt;= #{createdStartTime} </if>",
//			"<if test='createdEndTime != null' > and t.created_time &lt;= #{createdEndTime} </if>",
//			"<if test='!@org.springframework.util.CollectionUtils@isEmpty(idList)' > ",
//			" t.id IN"+ "<foreach collection='idList' item='id' open='(' separator=',' close=')'> #{id}</foreach>",
//			"</if>",
            "order by t.id desc",
            "</script>"})
    List<ExpressWaybillPO> queryList(ExpressWaybillPO listReqVO);

    /**
     * 批量新建快递运单（核心批量插入方法）
     *
     * @param waybillPOList 运单PO列表（需保证列表非空、元素非空）
     * @return 插入成功的条数（若返回值 < 列表长度，说明部分插入失败，需根据业务处理）
     */
    @Insert({
            "<script>",
            "INSERT INTO express_waybill (",
            "   waybill_no, file_type, waybill_object_key, waybill_url ",
            ")",
            "VALUES ",
            "<foreach collection='waybillPOList' item='po' separator=','>",
            "   (",
            "       #{po.waybillNo},  #{po.fileType}, #{po.waybillObjectKey}, #{po.waybillUrl}",
            "   )",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("waybillPOList") List<ExpressWaybillPO> waybillPOList);

    @Select({"<script>",
            "SELECT * from express_waybill t  WHERE  t.delete_flag=0  and waybill_no IN  ",
            "<foreach collection='waybillNos' item='no'  open='(' separator=',' close=')'>",
            "    #{no}",
            "</foreach>",
            "order by t.id desc",
            "</script>"})
    List<ExpressWaybillPO> findByWaybillNos(List<String> waybillNos);


}
