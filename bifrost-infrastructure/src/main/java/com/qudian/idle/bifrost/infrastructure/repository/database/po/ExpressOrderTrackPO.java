package com.qudian.idle.bifrost.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

import java.io.Serializable;

/**
 * 快递订单轨迹 PO
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain=true)
@TableName("`express_order_track`")
public class ExpressOrderTrackPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 内部订单号
     */
    private String orderNo;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 轨迹时间
     */
    private LocalDateTime trackTime;
    /**
     * 所在地点
     */
    private String location;
    /**
     * 轨迹描述
     */
    private String remark;
    /**
     * 异常描述
     */
    private String reasonName;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作码
     */
    private String opCode;
    /**
     * TODO@chr.需要关联主表的，子状态
     * 操作码映射后 3-已揽收 4-运输中 5-已派送 6-已签收 7-异常
     */
    private Integer opCodeMap;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 软删除标识，0表示未删除，1表示已删除
     */
    @TableLogic
    private Integer deleteFlag;
    /**
     * 变更来源：1-顺丰推送 2-主动查询
     */
    private Integer changeSource;

}
