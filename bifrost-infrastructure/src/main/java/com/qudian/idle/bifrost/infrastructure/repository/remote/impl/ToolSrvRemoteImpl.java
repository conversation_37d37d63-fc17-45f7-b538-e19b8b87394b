package com.qudian.idle.bifrost.infrastructure.repository.remote.impl;

import com.alibaba.fastjson.JSON;
import com.qudian.java.components.base.util.ApplicationContextUtil;
import com.qudian.java.components.base.vo.BaseResponseVo;
import com.qudian.idle.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.idle.bifrost.common.config.ToolRemoteApiConfig;
import com.qudian.idle.bifrost.common.enums.ExceptionEnum;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.utils.common.CommonUtil;
import com.qudian.idle.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ToolSrvRemoteImpl implements ToolSrvRemote {

    @Resource
    private ToolRemoteApiConfig toolRemoteApiConfig;

    @Override
    public String uploadFile(byte[] data, String fileName) {
        return uploadFileV2(data, fileName).getData().getSigned_url();
    }

    @Override
    public UploadFileRespVO uploadFileV2(byte[] data, String fileName) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();
        try {
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", fileName,
                            RequestBody.create(MediaType.parse("text/" + FilenameUtils.getExtension(fileName)),
                                    data))
                    .build();
            CommonUtil commonUtil = ApplicationContextUtil.getBean(CommonUtil.class);
            Request request = new Request.Builder()
                    .addHeader("keep-file-name", StringUtils.isNotBlank(fileName) ? "1" : "0")
                    .addHeader("directory", "idle/express/pdf/"+commonUtil.getProfiles())
                    .addHeader("expire-in", String.valueOf(60*60*24*365*3))
                    .url(toolRemoteApiConfig.getHost() + toolRemoteApiConfig.getUploadFileNotify())
                    .post(requestBody)
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            return getBaseResponseVo(response, UploadFileRespVO.class);
        } catch (IOException e) {
            throw new BizException("上传文件至出错!" + e.getMessage());
        }
    }

    /**
     * expire 整数，超时时间，默认60s，单位-秒
     * @param objectKey
     * @param expire
     * @return
     */
    @Override
    public UploadFileRespVO signedUrlV2(String objectKey, Integer expire) {
        try {
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();
            log.info("文件加签req={}", JSON.toJSONString(objectKey));
            String url = toolRemoteApiConfig.getHost() + toolRemoteApiConfig.getSignedUrl() + "?object_key=" + objectKey;
            if (Objects.nonNull(expire)) {
                url = url.concat("&expire_in=" + expire);
            }
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            return getBaseResponseVo(response, UploadFileRespVO.class);
        } catch (Exception e) {
            log.error("文件加签请求异常", e);
            throw new BizException(ExceptionEnum.SECOND_PARTY_ERROR);
        }
    }

    @Override
    public String signedUrl(String objectKey, Integer expire) {
        return signedUrlV2(objectKey, expire).getData().getSigned_url();
    }

    @NotNull
    private <T extends BaseResponseVo> T getBaseResponseVo(Response response, Class<T> className) throws IOException {
        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }
        if (ObjectUtils.isEmpty(response.body())) {
            throw new BizException("response body missing");
        }
        String responseBody = response.body().string();
        BaseResponseVo baseResponseVo = JSON.parseObject(responseBody, className);
        if (!Integer.valueOf("200").equals(baseResponseVo.getCode())) {
            throw new BizException(ExceptionEnum.THIRD_SYSTEM_ERROR, baseResponseVo.getMessage());
        }
        return (T) baseResponseVo;
    }
}
