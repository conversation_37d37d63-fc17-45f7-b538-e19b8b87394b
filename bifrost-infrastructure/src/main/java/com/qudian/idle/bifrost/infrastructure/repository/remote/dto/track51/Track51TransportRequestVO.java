package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Track51TransportRequestVO {
    /**
     * 澳洲邮政外部单号
     */
    private List<String> transportNos;
    /**
     * 订单编号
     */
    private List<String> orderNos;
    // 更新开始时间
    private Date startTime;
    // 更新结束时间
    private Date endTime;

    private Integer page;

    private Integer pageSize;
}
