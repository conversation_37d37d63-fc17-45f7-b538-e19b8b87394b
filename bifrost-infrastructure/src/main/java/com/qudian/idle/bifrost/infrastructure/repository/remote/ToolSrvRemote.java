package com.qudian.idle.bifrost.infrastructure.repository.remote;

import com.qudian.idle.bifrost.api.vo.response.tool.UploadFileRespVO;

public interface ToolSrvRemote {
    String uploadFile(byte[] data, String fileName);

    UploadFileRespVO uploadFileV2(byte[] data, String fileName);

    UploadFileRespVO signedUrlV2(String objectKey, Integer expire);

    String signedUrl(String objectKey, Integer expire);
    
}
