package com.qudian.idle.bifrost.infrastructure.repository;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/29
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.bifrost.infrastructure.repository.database.mapper.ExpressWaybillMapper;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressWaybillPO;
import org.springframework.stereotype.Repository;
import java.util.Optional;


import javax.annotation.Resource;
import java.util.List;


/**
 * @description 面单信息 Repository
 * <AUTHOR>
 * @date 2025-08-29
 */
@Repository
public class  ExpressWaybillRepository{
    @Resource
    private ExpressWaybillMapper expressWaybillMapper;

    public Optional<ExpressWaybillPO> selectById(Long id) {
        ExpressWaybillPO expressWaybillPO = expressWaybillMapper.selectById(id);
        return Optional.ofNullable(expressWaybillPO);
    }

    public boolean deleteById(Long id) {
        return SqlHelper.retBool(expressWaybillMapper.deleteById(id));
    }

//	public Optional<ExpressWaybillPO> selectByName(String name) {
//		QueryWrapper<ExpressWaybillPO> queryWrapper = new QueryWrapper<>();
//		queryWrapper.lambda().eq(ExpressWaybillPO::getName, name);
//			ExpressWaybillPO expressWaybillPO = expressWaybillMapper.selectOne(queryWrapper);
//		return Optional.ofNullable(expressWaybillPO);
//	}

    public List<ExpressWaybillPO> queryList(ExpressWaybillPO listReqVO) {
        return expressWaybillMapper.queryList(listReqVO);
    }

    public boolean insert(ExpressWaybillPO expressWaybillPO) {
        return SqlHelper.retBool(expressWaybillMapper.insert(expressWaybillPO));
    }
    public boolean batchInsert(List<ExpressWaybillPO> waybillPOList) {
        return SqlHelper.retBool(expressWaybillMapper.batchInsert(waybillPOList));
    }

    public List<ExpressWaybillPO> findByWaybillNos(List<String> waybillNos){
        return expressWaybillMapper.findByWaybillNos(waybillNos);
    }

    public boolean update(ExpressWaybillPO expressWaybillPO) {
        return SqlHelper.retBool(expressWaybillMapper.updateById(expressWaybillPO));
    }

}
