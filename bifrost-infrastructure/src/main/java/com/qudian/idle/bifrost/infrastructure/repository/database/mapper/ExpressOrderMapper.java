package com.qudian.idle.bifrost.infrastructure.repository.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description 快递订单 Mapper
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface ExpressOrderMapper extends BaseMapper<ExpressOrderPO> {
    @Select({"<script>",
            "SELECT * from express_order t where 1=1 ",
            "and t.delete_flag=0 ",
//			DATE_FORMAT(t.created_time,'%Y-%m-%d %H:%i:%s') created_time
//			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(name)' > and t.name LIKE CONCAT('%',#{name},'%') </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(source)' > and t.source = #{source} </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(orderNo)' > and t.order_no=  #{orderNo} </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(waybillNo)' > and t.waybill_no=  #{waybillNo} </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(sendContactPhone)' > and t.sender_phone=  #{sendContactPhone} </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(destContactPhone)' > and t.receiver_phone = #{destContactPhone} </if>",
			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(createdId)' > and t.created_id = #{createdId} </if>",
          "<if test='status!=null' > and t.status = #{status} </if>",
//			"<if test='createdStartTime != null' > and t.created_time &gt;= #{createdStartTime} </if>",
//			"<if test='createdEndTime != null' > and t.created_time &lt;= #{createdEndTime} </if>",
//			"<if test='!@org.springframework.util.CollectionUtils@isEmpty(idList)' > ",
//			" t.id IN"+ "<foreach collection='idList' item='id' open='(' separator=',' close=')'> #{id}</foreach>",
//			"</if>",
            "order by t.id desc",
            "</script>"})
    List<SearchExpressRespVO> queryList(SearchExpressReqVO searchExpressReqVO);

}
