package com.qudian.idle.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "Anula", namespace = "http://www.asmred.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class CancelDTO {
    @XmlElement(name = "docIn",namespace = "http://www.asmred.com/")
    private CancelDocIn cancelDocIn;
}
