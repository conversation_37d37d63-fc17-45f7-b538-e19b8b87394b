<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>bifrost</artifactId>
        <groupId>com.qudian.idle.bifrost</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>bifrost-application</artifactId>
    <version>${bifrost.base.version}</version>

    <properties>
        <commons-collections>3.2.2</commons-collections>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.qudian.idle.bifrost</groupId>
            <artifactId>bifrost-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
    </dependencies>
</project>
