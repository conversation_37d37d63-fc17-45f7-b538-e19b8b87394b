package com.qudian.idle.bifrost.application.factory.sfExpress.trace;

import com.google.common.collect.ImmutableMap;
import com.qudian.idle.bifrost.api.enums.ExpressOrderStatusEnum;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * 顺丰快递opCode配置管理
 * 
 * <AUTHOR>
 * @since 2025/8/28
 */
@Component
public class SfExpressOpCodeConfig {

    /**
     * 顺丰事件类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum SfExpressEventType {
        PICKUP("pickup", "已揽收", ExpressOrderStatusEnum.PICKED_UP, false),
        IN_TRANSIT("inTransit", "运送中", ExpressOrderStatusEnum.IN_TRANSIT, false),
        OUT_FOR_DELIVERY("outForDelivery", "派送中",  ExpressOrderStatusEnum.DELIVERING, false),
        DELIVERED("delivered", "已签收", ExpressOrderStatusEnum.SIGNED,  true),
        CANCEL("cancel", "已取消", ExpressOrderStatusEnum.CANCELLED,  false),
        EXCEPTION("exception", "异常", ExpressOrderStatusEnum.EXCEPTION, false),;

        public final String code;
        public final String description;
        public final ExpressOrderStatusEnum rootStatus;
        public final boolean needsDeliveryNotification; // 是否需要发送签收通知

        public boolean isDelivered() {
            return DELIVERED == this;
        }
    }

    /**
     * 顺丰opCode状态映射
     * 参考顺丰文档: https://open.sf-express.com/developSupport/734349?activeIndex=589678
     */
    private static final Map<String, SfExpressEventType> SF_OP_CODE_MAP = new ImmutableMap.Builder<String, SfExpressEventType>()
            // 收件相关
            .put("50", SfExpressEventType.PICKUP)           // 已收取快件
            .put("51", SfExpressEventType.PICKUP)           // 已收件
            .put("52", SfExpressEventType.PICKUP)           // 快件揽收
            // 运输中
            .put("10", SfExpressEventType.IN_TRANSIT)       // 快件在途中
            .put("11", SfExpressEventType.IN_TRANSIT)       // 快件运输中
            // 派送相关
            .put("70", SfExpressEventType.OUT_FOR_DELIVERY) // 正在派送途中
            .put("71", SfExpressEventType.OUT_FOR_DELIVERY) // 安排投递
            .put("72", SfExpressEventType.OUT_FOR_DELIVERY) // 开始派送
            .put("73", SfExpressEventType.OUT_FOR_DELIVERY) // 派送员出发
            // 签收 - 重要事件，需要特殊处理
            .put("80", SfExpressEventType.DELIVERED)        // 已签收
            .put("128", SfExpressEventType.DELIVERED)        // 客户从丰巢取件成功
            .put("658", SfExpressEventType.DELIVERED)        // 合作点已派件
            .put("701", SfExpressEventType.DELIVERED)        // 一票多件拍照收件
            .put("980", SfExpressEventType.DELIVERED)        // 第三方签收
            // 异常情况
            .put("90", SfExpressEventType.EXCEPTION)        // 快件异常
            .put("91", SfExpressEventType.EXCEPTION)        // 派送异常
            .build();

    /**
     * 根据opCode获取事件类型
     */
    public Optional<SfExpressEventType> getEventType(String opCode) {
        return Optional.ofNullable(SF_OP_CODE_MAP.get(opCode));
    }

    /**
     * 判断opCode是否为签收事件
     */
    public boolean isDeliveredEvent(String opCode) {
        return getEventType(opCode)
                .map(SfExpressEventType::isDelivered)
                .orElse(false);
    }

    /**
     * 判断opCode是否需要发送通知
     */
    public boolean needsDeliveryNotification(String opCode) {
        return getEventType(opCode)
                .map(SfExpressEventType::isNeedsDeliveryNotification)
                .orElse(false);
    }

    public String getOpCodeDescription(String opCode) {
        return getEventType(opCode)
                .map(SfExpressEventType::getDescription)
                .orElse("未知状态");
    }

    public ExpressOrderStatusEnum getRootStatus(String opCode) {
        return getEventType(opCode)
                .map(SfExpressEventType::getRootStatus)
                .orElse(ExpressOrderStatusEnum.UNKNOWN);
    }
}
