package com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto;

import com.qudian.idle.bifrost.api.enums.trace.TraceSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SfExpressRouteDTO implements Serializable {
    private List<SfExpressRouteDetailDTO> waybillRoute;
    private TraceSourceEnum source;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SfExpressRouteDetailDTO implements Serializable {
        private String waybillNo;
        private String acceptAddress;
        private String reasonName;
        private String orderId;
        private String acceptTime;
        private String remark;
        private String opCode;
        private String id;
        private String reasonCode;
    }
}
