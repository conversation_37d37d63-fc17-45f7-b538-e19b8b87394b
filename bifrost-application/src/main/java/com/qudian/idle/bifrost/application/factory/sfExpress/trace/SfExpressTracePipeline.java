package com.qudian.idle.bifrost.application.factory.sfExpress.trace;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.idle.bifrost.application.convertor.trace.SfExpressRouteStruct;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressTraceEvent;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.inputProcessor.IsfTraceInputProcessor;
import com.qudian.idle.bifrost.application.mq.dto.TransportDeliveredMqDTO;
import com.qudian.idle.bifrost.common.enums.trace.TraceEventMqTagEnum;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderRepository;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderTrackRepository;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderTrackPO;
import com.qudian.idle.bifrost.infrastructure.repository.mq.MqProducer;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline</p>
 * <p>文件描述: 顺丰物流轨迹，数据流处理</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明:
 *  进入Pipeline的数据流，已经统一业务格式，使用固定域：trace
 *  其余部分使用游离域：route
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Slf4j
@Component
public class SfExpressTracePipeline {
    @Resource
    private MqProducer mqProducer;
    @Resource
    private SfExpressOpCodeConfig opCodeConfig;
    @Resource
    private ExpressOrderTrackRepository trackRepository;
    @Resource
    private SfExpressRouteStruct routeStruct;
    @Resource
    private ExpressOrderRepository expressOrderRepository;

    /**
     * 回调接收轨迹
     *
     * @param routeDTO
     */
    public void flowByCallback(SfExpressRouteDTO routeDTO) {
        this.flow(routeDTO, new IsfTraceInputProcessor.SfTraceCallbackInputProcessor());
    }
    /**
     * 主动查询轨迹
     *
     * @param searchRoutesDTO
     */
    public void flowByQuery(SearchRoutesRespDTO searchRoutesDTO) {
        this.flow(searchRoutesDTO, new IsfTraceInputProcessor.SfTraceQueryInputProcessor());
    }
    public void flowByJSON(String routeJSON) {
        this.flow(routeJSON, new IsfTraceInputProcessor.SfTraceJSONInputProcessor());
    }

    /**
     * 对物流轨迹数据流，进行编排
     *
     * @param input
     * @param <T>
     */
    private <T> void flow(T input, IsfTraceInputProcessor<T> processor) {
        Mono.just(RouteProcessingContext.create(input, processor))
                .flatMap(this::ignoreExistTrace)
                .flatMap(this::insertTraceLog)
                .flatMap(this::parseChangeEvent)
                .flatMap(this::notifyChangeEvent)
                .subscribe(
                        context -> log.info("[sf_trace]flow.物流轨迹处理完成, cost: {} ms, metadata: {}",
                                context.getProcessTime().until(LocalDateTime.now(), ChronoUnit.MILLIS),
                                context.getMetadata()),
                        error -> log.error("[sf_trace]flow.物流轨迹流程处理失败: {}", error.getMessage(), error)
                );
    }

    /**
     * 忽略已存在的轨迹
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> ignoreExistTrace(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            Map<String /* waybillNo */, Integer /* removedCnt */> routeOpTable = Maps.newHashMap();
            Map<String, SfExpressRouteDTO> parsedData = context.getParsedData();
            //和数据表里已有的轨迹对比，当前这条轨迹时间如果小于数据库已存在的最大轨迹时间，则不处理
            Set<String> waybillNos = parsedData.keySet();
            log.info("[sf_trace]ignoreExistTrace query latest trace, waybillNos: {}", waybillNos);
            Map<String, ExpressOrderTrackPO> traceTable = trackRepository.mapByWaybillNoLatest(waybillNos);
            for (Map.Entry<String, SfExpressRouteDTO> routeEntry : parsedData.entrySet()) {
                ExpressOrderTrackPO latestRoute = traceTable.get(routeEntry.getKey());
                if (null == latestRoute) {
                    continue;
                }
                SfExpressRouteDTO routeDto = routeEntry.getValue();
                if (null == routeDto || CollectionUtil.isEmpty(routeDto.getWaybillRoute())) {
                    continue;
                }
                int allCnt = routeDto.getWaybillRoute().size();
                routeDto.getWaybillRoute()
                        .removeIf(routeDetail -> {
                            LocalDateTime traceTime = LocalDateTime.parse(routeDetail.getAcceptTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                            return !traceTime.isAfter(latestRoute.getTrackTime());
                        });
                routeOpTable.put(routeEntry.getKey(), allCnt - routeDto.getWaybillRoute().size());
            }
            return context.withMetadata(RouteProcessingContext.TraceMetadata.IGNORE_TRACE_TABLE, routeOpTable);
        });
    }

    /**
     * 插入轨迹日志
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> insertTraceLog(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            Map<String, SfExpressRouteDTO> parsedData = context.getParsedData();
            if (CollectionUtil.isEmpty(parsedData)) {
                log.info("[sf_trace]insertTraceLog parsedData is empty, skip insert!");
                return context.withMetadata(RouteProcessingContext.TraceMetadata.INSERTED_TRACE_CNT, 0);
            }
            int insertedCnt = 0;
            for (SfExpressRouteDTO routeDTO : parsedData.values()) {
                List<ExpressOrderTrackPO> trackPOs = routeDTO.getWaybillRoute().stream()
                        //TODO@chr 批量的原始报文，在每个元素都存储，不合理
                        .map(route -> {
                            ExpressOrderTrackPO trackPO = routeStruct.detailDto2Po(route, context.getBodyJSON());
                            trackPO.setChangeSource(routeDTO.getSource().code);
                            trackPO.setOpCodeMap(opCodeConfig.getRootStatus(trackPO.getOpCode()).getCode());
                            return trackPO;
                        })
                        .collect(Collectors.toList());
                insertedCnt += trackPOs.size();
                //noinspection BlockingMethodInNonBlockingContext
                trackRepository.saveBatch(trackPOs);
            }
            return context.withMetadata(RouteProcessingContext.TraceMetadata.INSERTED_TRACE_CNT, insertedCnt);
        });
    }

    /**
     * 解析运单状态变更事件
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> parseChangeEvent(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            Map<String, SfExpressRouteDTO> parsedData = context.getParsedData();
            if (CollectionUtil.isEmpty(parsedData)) {
                log.info("[sf_trace].parseChangeEvent parsedData is empty, skip parse!");
                return context.withMetadata(RouteProcessingContext.TraceMetadata.EVENT_CNT, 0);
            }
            List<SfExpressTraceEvent> events = new ArrayList<>();
            //取最新的单个节点，作为通知事件
            for (Map.Entry<String /* waybillNo */, SfExpressRouteDTO> routeEntry : parsedData.entrySet()) {
                if (Objects.isNull(routeEntry.getValue()) || CollectionUtil.isEmpty(routeEntry.getValue().getWaybillRoute())) {
                    continue;
                }
                List<SfExpressRouteDTO.SfExpressRouteDetailDTO> waybillRoutes = routeEntry.getValue().getWaybillRoute();
                waybillRoutes.sort(Comparator.comparing(SfExpressRouteDTO.SfExpressRouteDetailDTO::getAcceptTime).reversed());

                SfExpressRouteDTO.SfExpressRouteDetailDTO route = waybillRoutes.get(0);
                String opCode = route.getOpCode();
                Optional<SfExpressOpCodeConfig.SfExpressEventType> eventTypeOpt = opCodeConfig.getEventType(opCode);
                if (!eventTypeOpt.isPresent()) {
                    log.warn("[sf_trace].parseChangeEvent unrecognized opCode: {}", opCode);
                    continue;
                }
                SfExpressOpCodeConfig.SfExpressEventType eventType = eventTypeOpt.get();
                SfExpressTraceEvent event = new SfExpressTraceEvent()
                        .setMailno(route.getWaybillNo())
                        .setOpCode(opCode)
                        .setEventType(eventType)
                        .setAcceptTime(route.getAcceptTime())
                        .setAcceptAddress(route.getAcceptAddress())
                        .setRemark(route.getRemark())
                        .setOrderId(route.getOrderId());
                events.add(event);
            }
            //先按事件聚类，可执行批量更新
            Map<SfExpressOpCodeConfig.SfExpressEventType, List<SfExpressTraceEvent>> eventTable = events.stream().collect(Collectors.groupingBy(SfExpressTraceEvent::getEventType));
            return context.setEventTable(eventTable).withMetadata(RouteProcessingContext.TraceMetadata.EVENT_CNT, events.size());
        });
    }

    /**
     * 通知运单状态变更事件
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> notifyChangeEvent(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            Map<SfExpressOpCodeConfig.SfExpressEventType, List<SfExpressTraceEvent>> eventTable = context.getEventTable();
            if (MapUtil.isEmpty(eventTable)) {
                log.info("[sf_trace].notifyChangeEvent events is empty, skip notify!");
                return context.withMetadata(RouteProcessingContext.TraceMetadata.MQ_SENT_CNT, 0);
            }
            int mqSentCnt = 0;
            for (Map.Entry<SfExpressOpCodeConfig.SfExpressEventType, List<SfExpressTraceEvent>> eventEntry : eventTable.entrySet()) {
                log.info("[sf_trace].notifyChangeEvent launching event: {}， size: {}", eventEntry.getKey().description, eventEntry.getValue().size());
                if (CollectionUtil.isEmpty(eventEntry.getValue())) {
                    continue;
                }
                //更新主表状态
                List<String> waybillNos = eventEntry.getValue().stream().map(SfExpressTraceEvent::getMailno).collect(Collectors.toList());
                expressOrderRepository.batchUpdateStatus(waybillNos, eventEntry.getKey().rootStatus.getCode());

                for (SfExpressTraceEvent event : eventEntry.getValue()) {
                    // 发送状态变更MQ
                    if (!event.getEventType().isNeedsDeliveryNotification()) {
                        continue;
                    }
                    try {
                        mqProducer.sendEntityMsg(buildDeliveredMqDTO(event), TraceEventMqTagEnum.DELIVERED.tag, event.getMailno());
                        ++mqSentCnt;
                        log.info("[sf_trace]notifyChangeEvent.发送轨迹变更MQ: 运单号={}, 事件类型={}, opCode={}", event.getMailno(), event.getEventType().getDescription(), event.getOpCode());
                    } catch (Exception e) {
                        log.error("[sf_trace]notifyChangeEvent.发送轨迹变更MQ失败: 运单号={}, 错误={}", event.getMailno(), e.getMessage(), e);
                    }
                }
            }
            return context.withMetadata(RouteProcessingContext.TraceMetadata.MQ_SENT_CNT, mqSentCnt);
        });
    }

    /**
     * TODO@chr 构建签收MQ消息
     */
    private TransportDeliveredMqDTO buildDeliveredMqDTO(SfExpressTraceEvent event) {
        return TransportDeliveredMqDTO.builder()
                .orderNumber(event.getOrderId())
                .articleId(event.getMailno())
                .forwarderType("SF")
                .status(TransshipmentStatusEnum.DELIVERED.getCode())
                .carrier("顺丰速运")
                .date(event.getAcceptTime())
                .eventTime(parseEventTime(event.getAcceptTime()))
                .build();
    }

    /**
     * 解析事件时间为毫秒时间戳
     */
    private Long parseEventTime(String acceptTime) {
        try {
            if (StringUtils.isBlank(acceptTime)) {
                return System.currentTimeMillis();
            }
            // 顺丰时间格式: 2020-05-11 16:56:54
            LocalDateTime dateTime = LocalDateTime.parse(acceptTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            log.warn("[sf_trace]parseEventTime.解析时间失败: {}, 使用当前时间", acceptTime, e);
            return System.currentTimeMillis();
        }
    }

}
