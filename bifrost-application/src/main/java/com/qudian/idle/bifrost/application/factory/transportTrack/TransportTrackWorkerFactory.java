package com.qudian.idle.bifrost.application.factory.transportTrack;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * {@inheritDoc} 转运同步任务工厂类
 *
 * <AUTHOR>
 * @since 2023/8/8
 **/
@Component
public class TransportTrackWorkerFactory {

    private static final Map<String, BaseTransportTrackWorker> FACTORY_MAP = new HashMap<>();

    public static void register(String carrier, BaseTransportTrackWorker worker) {
        FACTORY_MAP.put(carrier, worker);
    }

    public BaseTransportTrackWorker getWorker(String carrier) {
        return FACTORY_MAP.get(carrier);
    }

}
