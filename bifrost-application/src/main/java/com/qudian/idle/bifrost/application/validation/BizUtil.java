package com.qudian.idle.bifrost.application.validation;

import com.qudian.idle.bifrost.common.enums.ExceptionEnum;
import com.qudian.idle.bifrost.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;

public final class BizUtil {

    private BizUtil(){}

    public static  <T> void requireNonNull(T obj, String message) {
        if (obj == null)
            throw new BizException(ExceptionEnum.PARAMETER_VERIFICATION, message);
    }


    public static void requireNonBlank(String obj, String message) {
        if (StringUtils.isBlank(obj))
            throw new BizException(ExceptionEnum.PARAMETER_VERIFICATION, message);
    }


    public static void requireFalse(Boolean obj, String message) {
        if (obj)
            throw new BizException(ExceptionEnum.PARAMETER_VERIFICATION, message);
    }




}
