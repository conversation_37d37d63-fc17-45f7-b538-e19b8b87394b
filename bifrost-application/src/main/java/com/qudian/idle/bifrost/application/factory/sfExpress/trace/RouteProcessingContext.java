package com.qudian.idle.bifrost.application.factory.sfExpress.trace;

import com.google.common.collect.Maps;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressTraceEvent;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.inputProcessor.IsfTraceInputProcessor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <p>文件名称:com.qudian.idle.bifrost.application.factory.sfExpress.trace.RouteProcessingContext</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Data
@Accessors(chain = true)
public class RouteProcessingContext {
    private Map<String /* waybillNo */, SfExpressRouteDTO> parsedData;  // 解析后，统一格式的数据
    private Map<SfExpressOpCodeConfig.SfExpressEventType, List<SfExpressTraceEvent>> eventTable; // 轨迹事件
    private EnumMap<TraceMetadata, Object> metadata;
    private String bodyJSON;    // 原始报文
    private LocalDateTime processTime;

    public RouteProcessingContext() {
        this.metadata = Maps.newEnumMap(TraceMetadata.class);
        this.processTime = LocalDateTime.now();
        this.eventTable = Maps.newHashMap();
    }

    public static <T> RouteProcessingContext create(T input, IsfTraceInputProcessor<T> processor) {
        RouteProcessingContext context = new RouteProcessingContext();
        processor.parseInput(context, input);
        return context;
    }

    public RouteProcessingContext withMetadata(TraceMetadata key, Object value) {
        metadata.put(key, value);
        return this;
    }

    public RouteProcessingContext withErrorMetaData(Object val) {
        this.metadata.put(TraceMetadata.VALIDATION_STATUS, "FAILED");
        this.metadata.put(TraceMetadata.ERROR_MESSAGE, val);
        return this;
    }

    public enum TraceMetadata {
        //----------------pipeline----------------
        /**
         * 忽略已存在的轨迹
         */
        IGNORE_TRACE_TABLE,
        /**
         * 插入轨迹日志
         */
        INSERTED_TRACE_CNT,
        /**
         * 解析运单状态变更事件
         */
        EVENT_CNT,
        /**
         * 通知运单状态变更事件
         */
        MQ_SENT_CNT,
        //---------------sfTraceInputProcessor----------------
        VALIDATION_STATUS,
        ERROR_MESSAGE,
        ROUTE_COUNT
    }
}
