package com.qudian.idle.bifrost.application.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.common.exception.BizException;

import java.util.Optional;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
public class ExpressValidation {

    public static void batchPrintVerify(BatchPrintReqVO batchPrintReqVO) {
        if (CollUtil.isEmpty(batchPrintReqVO.getBatchRequest())) {
            throw new BizException("batchRequest must not be null.");
        }
        if (batchPrintReqVO.getBatchRequest().stream().anyMatch(t -> StrUtil.isBlank(t.getWaybillNo()))) {
            throw new BizException("batchRequest.waybillNo must not be null.");
        }
    }


    public static void createVerify(CreateExpressReqVO createExpressReqVO) {
        Optional.ofNullable(createExpressReqVO).orElseThrow(() -> new BizException("createExpressReqVO must not be null."));

        BizUtil.requireNonBlank(createExpressReqVO.getOrderNo(), "orderNo must not be null.");
        if (CollUtil.isEmpty(createExpressReqVO.getCargoDetails())) {
            throw new BizException("cargoDetails must not be null.");
        }
        if (createExpressReqVO.getCargoDetails().stream().anyMatch(t -> StrUtil.isBlank(t.getName()))) {
            throw new BizException("cargoDetails.name must not be null.");
        }

        try {
            if (StrUtil.isNotBlank(createExpressReqVO.getSendStartTm())) {
                DateUtil.parse(createExpressReqVO.getSendStartTm(), DatePattern.NORM_DATETIME_PATTERN);
//                LocalDateTime.parse(createExpressReqVO.getSendStartTm(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            throw new BizException("sendStartTm must be yyyy-MM-dd HH:mm:ss format.");
        }


        CreateExpressReqVO.ContactInfo sendContactInfo = createExpressReqVO.getSendContactInfo();
        BizUtil.requireNonNull(sendContactInfo, "sendContactInfo must not be null.");
        BizUtil.requireNonBlank(sendContactInfo.getContact(), "sendContactInfo.contact must not be null.");
        BizUtil.requireNonBlank(sendContactInfo.getProvince(), "sendContactInfo.province must not be null.");
        BizUtil.requireNonBlank(sendContactInfo.getCity(), "sendContactInfo.city must not be null.");
        BizUtil.requireNonBlank(sendContactInfo.getAddress(), "sendContactInfo.address must not be null.");
        BizUtil.requireNonBlank(sendContactInfo.getMobile(), "sendContactInfo.mobile must not be null.");
        BizUtil.requireFalse(!PhoneValidator.isValidPhone(sendContactInfo.getMobile()), "寄件方手机号不正确.");

        CreateExpressReqVO.ContactInfo destContactInfo = createExpressReqVO.getDestContactInfo();
        BizUtil.requireNonNull(destContactInfo, "destContactInfo must not be null.");
        BizUtil.requireNonNull(destContactInfo.getContact(), "destContactInfo.contact must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getAddress(), "destContactInfo.address must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getProvince(), "destContactInfo.province must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getCity(), "destContactInfo.city must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getMobile(), "destContactInfo.mobile must not be null.");
        BizUtil.requireFalse(!PhoneValidator.isValidPhone(destContactInfo.getMobile()), "收件方手机号不正确.");
    }


    public static void updateVerify(UpdateExpressReqVO updateExpressReqVO) {
        Optional.ofNullable(updateExpressReqVO).orElseThrow(() -> new BizException("updateExpressReqVO must not be null."));

        BizUtil.requireNonBlank(updateExpressReqVO.getWaybillNo(), "waybillNo must not be null.");
        if (CollUtil.isEmpty(updateExpressReqVO.getCargoDetails())) {
            throw new BizException("cargoDetails must not be null.");
        }
        if (updateExpressReqVO.getCargoDetails().stream().anyMatch(t -> StrUtil.isBlank(t.getName()))) {
            throw new BizException("cargoDetails.name must not be null.");
        }

        try {
            if (StrUtil.isNotBlank(updateExpressReqVO.getSendStartTm())) {
                DateUtil.parse(updateExpressReqVO.getSendStartTm(), DatePattern.NORM_DATETIME_PATTERN);
//                LocalDateTime.parse(updateExpressReqVO.getSendStartTm(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            throw new BizException("sendStartTm must be yyyy-MM-dd HH:mm:ss format.");
        }

        CreateExpressReqVO.ContactInfo destContactInfo = updateExpressReqVO.getDestContactInfo();
        BizUtil.requireNonNull(destContactInfo, "destContactInfo must not be null.");
        BizUtil.requireNonNull(destContactInfo.getContact(), "destContactInfo.contact must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getAddress(), "destContactInfo.address must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getProvince(), "destContactInfo.province must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getCity(), "destContactInfo.city must not be null.");
        BizUtil.requireNonBlank(destContactInfo.getMobile(), "destContactInfo.mobile must not be null.");
        BizUtil.requireFalse(!PhoneValidator.isValidPhone(destContactInfo.getMobile()), "收件方手机号不正确.");
    }

}
