package com.qudian.idle.bifrost.application.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * {@inheritDoc} 转运签收
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportDeliveredMqDTO implements Serializable {
    private static final long serialVersionUID = -6092071518307775364L;

    /**
     * 运单id
     */
    private String orderNumber;
    /**
     * 物流跟踪id
     */
    private String articleId;
    /**
     * 服务商类型
     */
    private String forwarderType;
    /**
     * 转运物流状态
     */
    private String status;
    /**
     * 承运商
     */
    private String carrier;
    /**
     * 时间
     */
    private String date;
    /**
     * 毫秒时间戳
     */
    private Long eventTime;
    /**
     * 时区
     */
    private String eventTimeZone;
    /**
     * 邮编
     */
    private String eventZIPCode;
    /**
     * 城市
     */
    private String eventCity;
    /**
     * 州
     */
    private String eventState;
    /**
     * 国家
     */
    private String eventCountry;
    /**
     * 公司名称
     */
    private String firmName;

    private String gmtOffset;
}
