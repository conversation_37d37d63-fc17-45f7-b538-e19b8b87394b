package com.qudian.idle.bifrost.application.validation;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 手机号校验工具类（基于Hutool）
 */
public class PhoneValidator {

    // 匹配国际手机号的正则：^+?[1-9]\d{1,14}$
    private static final String PHONE_REGEX = "^\\+?[1-9]\\d{8,14}$";

    /**
     * 校验手机号是否符合规则
     * @param phone 手机号（支持带+号的国际手机号，如+8613800138000）
     * @return true-符合规则，false-不符合
     */
    public static boolean isValidPhone(String phone) {
        // 先判断是否为空
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        // 使用Hutool的ReUtil匹配正则
        return ReUtil.isMatch(PHONE_REGEX, phone);
    }

    // 测试示例
    public static void main(String[] args) {
        System.out.println(isValidPhone("13800138000"));      // true（国内手机号）
        System.out.println(isValidPhone("+8613800138000"));   // true（带+86的国内手机号）
        System.out.println(isValidPhone("+12125551234"));     // true（国际手机号）
        System.out.println(isValidPhone("123"));              // false（长度不足）
        System.out.println(isValidPhone("+0123456789"));      // false（以0开头，不符合[1-9]）
        System.out.println(isValidPhone(null));                // false（空值）
    }
}
