package com.qudian.idle.bifrost.application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.vo.PagingList;
import com.qudian.idle.bifrost.api.vo.request.*;
import com.qudian.idle.bifrost.api.vo.response.DeliverySettleListResVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.api.vo.response.QuerySupportCarriersRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;

import javax.validation.Valid;

public interface OrderTransportService extends IService<OrderTransportPO> {

    BaseResponseDTO<String> cancelTransport(CancelTransportReqVO reqVO);

    /**
     * 转运单个预下单
     * @param reqVO
     * @return
     */
    BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(@Valid PushSingleTransportReqVO reqVO);

    /**
     * 打印单个面单(返回面单下载地址)
     * @param reqVO
     * @return
     */
    PrintLabelResponseVO printSingleLabel(@Valid SingleOrderTransportReqVO reqVO);

    /**
     * 单个确认下单
     *
     * @param reqVO
     * @return
     */
    BaseResponseDTO<String> dispatchSingleTransport(@Valid SingleOrderTransportReqVO reqVO);


    QuerySupportCarriersRespVO querySupportCarriers(QuerySupportCarriersReqVO reqVO);

    QuerySupportCarriersRespVO querySupportCarriersByCountry(SupportCarriersByCountryVO reqVO);

    PagingList<DeliverySettleListResVO> querySettleList(DeliverySettleListReqVO reqVO);
}
