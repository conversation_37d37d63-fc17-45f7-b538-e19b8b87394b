package com.qudian.idle.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.idle.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.idle.bifrost.infrastructure.repository.remote.CTTService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: yangxinye
 * @Date: 2024/4/25
 * @Version: 1.0.0
 **/
@Component
@Slf4j
public class CttTransportTrackWorker extends BaseTransportTrackWorker {


    @Resource
    private CTTService cttService;

    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("0000", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("0010", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("0020", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("0030", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("0300", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("0500", TransshipmentStatusEnum.IN_TRANSIT)
        .put("0700", TransshipmentStatusEnum.IN_TRANSIT)
        .put("0900", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1000", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1200", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1500", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1700", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1800", TransshipmentStatusEnum.IN_TRANSIT)
        .put("1900", TransshipmentStatusEnum.IN_TRANSIT)
        .put("2400", TransshipmentStatusEnum.IN_TRANSIT)
        .put("2700", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("2300", TransshipmentStatusEnum.AWAITING_COLLECTION)
        .put("2900", TransshipmentStatusEnum.AWAITING_COLLECTION)

        // 转运已签收
        .put("2100", TransshipmentStatusEnum.DELIVERED)
        .put("2200", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("0400", TransshipmentStatusEnum.ABNORMAL)
        .put("0600", TransshipmentStatusEnum.ABNORMAL)
        .put("1600", TransshipmentStatusEnum.ABNORMAL)
        .put("2500", TransshipmentStatusEnum.ABNORMAL)
        .put("2600", TransshipmentStatusEnum.ABNORMAL)
        .put("3000", TransshipmentStatusEnum.ABNORMAL)
        .build();

    @Override
    public String support() {
        return CarrierTypeEnum.CTT.getName();
    }

    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }


    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        return this::queryTrack;
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1)));
    }

    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(result.getStatus(), TransshipmentStatusEnum.UNKNOWN).getCode();
    }


    private List<TransportTrackRespVO.TrackingResult> queryTrack(List<OrderTransportPO> orderTransportPOList) {
        List<ShippingStatusReqDTO.ShippingOrderInfo> collect = orderTransportPOList.stream()
            .map(o -> new ShippingStatusReqDTO.ShippingOrderInfo()
                .setOrderId(o.getOrderNumber())
                .setTrackingId(o.getArticleId()))
            .collect(Collectors.toList());

        ShippingStatusReqDTO reqDTO = new ShippingStatusReqDTO()
            .setOrderInfoList(collect);
        TransportTrackRespVO transportTrackRespVO = cttService.shippingStatus(reqDTO);
        return transportTrackRespVO.getTrackingResults();
    }

}
