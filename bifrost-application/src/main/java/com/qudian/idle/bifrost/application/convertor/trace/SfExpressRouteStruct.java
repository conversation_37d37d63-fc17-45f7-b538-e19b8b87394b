package com.qudian.idle.bifrost.application.convertor.trace;

import com.qudian.idle.bifrost.api.vo.request.express.trace.SfExpressTraceCallbackReqVO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderTrackPO;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <p>文件名称:com.qudian.idle.bifrost.application.convertor.trace.SfExpressRouteStruct</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/29
 */
@Mapper
public interface SfExpressRouteStruct {

    @Mappings({
            @Mapping(target = "waybillNo", source = "waybillNo"),
            @Mapping(target = "location", source = "acceptAddress"),
            @Mapping(target = "reasonName", source = "reasonName"),
            @Mapping(target = "trackTime", source = "acceptTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "remark", source = "remark"),
            @Mapping(target = "opCode", source = "opCode"),
            @Mapping(target = "content", expression = "java(originalBody)")
    })
    ExpressOrderTrackPO detailDto2Po(SfExpressRouteDTO.SfExpressRouteDetailDTO routeDetailDTO, @Context String originalBody);

    @Mappings({
            @Mapping(target = "waybillNo", source = "mailno"),
            @Mapping(target = "orderId", source = "orderid"),
    })
    SfExpressRouteDTO.SfExpressRouteDetailDTO detailVo2Dto(SfExpressTraceCallbackReqVO.SfExpressRouteDetailDTO vo);

    SfExpressRouteDTO voToDto(SfExpressTraceCallbackReqVO vo);
}
