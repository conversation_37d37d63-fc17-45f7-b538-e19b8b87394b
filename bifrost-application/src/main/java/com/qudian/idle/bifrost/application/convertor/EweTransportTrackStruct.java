package com.qudian.idle.bifrost.application.convertor;

import cn.hutool.core.date.DateUtil;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.EweShippingStatusRespDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

/**
 * {@inheritDoc} 物流轨迹对象转换
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Mapper(imports = {DateUtil.class, CarrierTypeEnum.class})
public interface EweTransportTrackStruct {

    @Mapping(source = "dto.tracking_results", target = "trackingResults")
    TransportTrackRespVO toVO(EweShippingStatusRespDTO dto);

    @Mappings({
        @Mapping(source = "dto.tracking_id", target = "articleId"),
        @Mapping(source = "dto.sender_reference", target = "orderNumber"),
        @Mapping(target = "carrier", expression = "java(CarrierTypeEnum.AUS_POST.getName())")
    })
    TransportTrackRespVO.TrackingResult toVOResult(EweShippingStatusRespDTO.TrackingResult dto);

    @Mapping(source = "dto.description", target = "desc")
    @Mapping(expression = "java(DateUtil.parseDateTime(dto.getDate()).getTime())", target = "eventTime")
    @Mapping(constant = "AU", target = "eventCountry")
    TransportTrackRespVO.TrackingResult.Event toVOEvent(EweShippingStatusRespDTO.TrackingResult.TrackableItem.Event dto);

    @AfterMapping
    default void afterMapping(@MappingTarget TransportTrackRespVO.TrackingResult.TrackingResultBuilder po, EweShippingStatusRespDTO.TrackingResult vo) {
        if (CollectionUtils.isEmpty(vo.getTrackable_items())) {
            return;
        }
        // 目前只有一单对应一个包裹的场景，这里直接取第一个
        EweShippingStatusRespDTO.TrackingResult.TrackableItem trackableItem = vo.getTrackable_items().get(0);
        po.events(trackableItem.getEvents().stream()
            .map(this::toVOEvent)
            .collect(Collectors.toList()));
    }

}
