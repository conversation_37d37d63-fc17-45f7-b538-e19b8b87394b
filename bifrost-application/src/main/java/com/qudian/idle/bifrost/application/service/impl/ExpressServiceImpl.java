package com.qudian.idle.bifrost.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.enums.ExpressOrderStatusEnum;
import com.qudian.idle.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CancelExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchSingleRouteReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CancelExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.UpdateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.trace.SearchSingleRouteRespVO;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.application.validation.ExpressValidation;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.common.utils.common.StringUtil;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderRepository;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderTrackRepository;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressWaybillRepository;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderTrackPO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressWaybillPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import com.qudian.idle.bifrost.infrastructure.repository.remote.impl.sf.factory.SfApiHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Service
@Slf4j
public class ExpressServiceImpl implements ExpressService {

    @Resource
    private SfApiHandlerFactory sfApiHandlerFactory;

    @Resource
    private ExpressOrderRepository expressOrderRepository;

    @Resource
    private ExpressWaybillRepository expressWaybillRepository;

    @Resource
    private ExpressOrderTrackRepository trackRepository;

    @Override
    public CreateExpressRespVO create(CreateExpressReqVO createExpressReqVO) {
        log.info("op=start_ExpressServiceImpl.save, saveExpressReqVO={}", JSON.toJSONString(createExpressReqVO));
        StringUtil.removeSpaces(createExpressReqVO);
        ExpressValidation.createVerify(createExpressReqVO);

        //根据订单号生成快递订单号
        String expressOrderNo = createExpressReqVO.getOrderNo() + "_" + System.currentTimeMillis();
        createExpressReqVO.setExpressOrderNo(expressOrderNo);

        AbstractSfApiTemplate handler = sfApiHandlerFactory.getHandler(SfApiCodeEnum.CREATE_ORDER);
        CreateExpressRespVO createExpressRespVO = (CreateExpressRespVO) handler.execute(createExpressReqVO);

        //保存快递订单
        ExpressOrderPO expressOrderPO = ExpressOrderPO.builder()
                .expressJson(JSON.toJSONString(createExpressReqVO))
                .expressOrderNo(createExpressReqVO.getExpressOrderNo())
                .orderNo(createExpressReqVO.getOrderNo())
                .source(createExpressReqVO.getSource())
                .senderPhone(createExpressReqVO.getSendContactInfo().getMobile())
                .receiverPhone(createExpressReqVO.getDestContactInfo().getMobile())
                .waybillNo(createExpressRespVO.getWaybillNo())
                .status(ExpressOrderStatusEnum.ORDERED.getCode())
                .requestId(createExpressReqVO.getRequestId())
                .createdId(createExpressReqVO.getCreatedId())
                .createdName(createExpressReqVO.getCreatedName())
                .build();
        expressOrderRepository.insert(expressOrderPO);

        return createExpressRespVO;
    }

    @Override
    public List<SearchExpressRespVO> searchExpress(SearchExpressReqVO searchExpressReqVO) {
        List<SearchExpressRespVO> expressRespVOList = expressOrderRepository.queryList(searchExpressReqVO);
        return expressRespVOList;
    }


    @Override
    public BatchPrintRespVO batchPrint(BatchPrintReqVO batchPrintReqVO) {
        StringUtil.removeSpaces(batchPrintReqVO);
        ExpressValidation.batchPrintVerify(batchPrintReqVO);

        // 获取所有请求的面单号
        List<String> allWaybillNos = batchPrintReqVO.getBatchRequest().stream().map(BatchPrintReqVO.BatchPrintReq::getWaybillNo).collect(Collectors.toList());
        log.info("开始处理批量打印请求，共{}个面单", allWaybillNos.size());

        // 1. 从数据库查询已存在的面单
        List<ExpressWaybillPO> existingWaybills = expressWaybillRepository.findByWaybillNos(allWaybillNos);
        Set<String> existingWaybillNos = existingWaybills.stream()
                .map(ExpressWaybillPO::getWaybillNo)
                .collect(Collectors.toSet());

        log.info("数据库中已存在{}个面单", existingWaybillNos.size());

        // 2. 筛选出需要调用接口获取的面单号（不存在于数据库中的）
        List<String> needFetchWaybillNos = allWaybillNos.stream()
                .filter(no -> !existingWaybillNos.contains(no))
                .collect(Collectors.toList());

        List<ExpressWaybillPO> newWaybills = new ArrayList<>();

        // 3. 调用接口获取不存在的面单信息
        if (!needFetchWaybillNos.isEmpty()) {
            log.info("需要调用接口获取{}个面单信息", needFetchWaybillNos.size());

            // 复制请求对象并设置需要获取的面单号
            BatchPrintReqVO fetchReqVO = new BatchPrintReqVO();
            fetchReqVO.setBatchRequest(needFetchWaybillNos.stream().map(no -> new BatchPrintReqVO.BatchPrintReq(no)).collect(Collectors.toList()));

            // 调用接口
            AbstractSfApiTemplate handler = sfApiHandlerFactory.getHandler(SfApiCodeEnum.BATCH_PRINT_WAYBILLS);
            BatchPrintRespVO apiRespVO = (BatchPrintRespVO) handler.execute(fetchReqVO);

            // 转换为PO对象
            if (apiRespVO != null && apiRespVO.getBatchResponse() != null) {
                newWaybills = apiRespVO.getBatchResponse().stream()
                        .map(t -> ExpressWaybillPO.builder().fileType("pdf")
                                .waybillNo(t.getWaybillNo()).waybillUrl(t.getPdfUrl()).requestId(batchPrintReqVO.getRequestId())
                                .waybillObjectKey(t.getPdfObjectKey()).build())
                        .collect(Collectors.toList());

                // 4. 将新获取的面单信息落库
                if (!newWaybills.isEmpty()) {
                    log.info("将{}个新获取的面单信息落库", newWaybills.size());
                    // 批量插入，考虑分批处理大数据量
                    batchInsertWithBatchSize(newWaybills, 50);
                }
            }
        }

        // 5. 合并已存在的和新获取的面单信息
        List<ExpressWaybillPO> allWaybills = new ArrayList<>();
        allWaybills.addAll(existingWaybills);
        allWaybills.addAll(newWaybills);

        // 6. 按原始请求顺序排序并构建返回结果
        Map<String, ExpressWaybillPO> waybillMap = allWaybills.stream()
                .collect(Collectors.toMap(ExpressWaybillPO::getWaybillNo, Function.identity()));

        // 检查是否有缺失的面单
        List<String> missingWaybills = allWaybillNos.stream()
                .filter(no -> !waybillMap.containsKey(no))
                .collect(Collectors.toList());

        if (!missingWaybills.isEmpty()) {
            log.warn("部分面单获取失败: {}", missingWaybills);
            // 可以根据业务需求决定是否抛出异常或继续处理
//            throw new BizException("面单获取失败: " + String.join(",", missingWaybills));
        }

        // 构建响应对象
        BatchPrintRespVO result = new BatchPrintRespVO();
        List<BatchPrintRespVO.BatchPrintResp> batchResponses = allWaybillNos.stream().map(no -> {
            ExpressWaybillPO po = waybillMap.get(no);
            return BatchPrintRespVO.BatchPrintResp.builder().waybillNo(po.getWaybillNo()).pdfUrl(po.getWaybillUrl())
                    .pdfObjectKey(po.getWaybillObjectKey()).build();
        }).collect(Collectors.toList());
        result.setBatchResponse(batchResponses);

        log.info("批量打印处理完成，总数量: {}, 成功: {}, 失败: {}",
                allWaybillNos.size(), allWaybills.size(), missingWaybills.size());

        return result;
    }


    /**
     * 分批批量插入，避免单次插入数据量过大
     */
    private void batchInsertWithBatchSize(List<ExpressWaybillPO> list, int batchSize) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        int totalSize = list.size();
        int batches = (totalSize + batchSize - 1) / batchSize;

        for (int i = 0; i < batches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, totalSize);
            List<ExpressWaybillPO> subList = list.subList(start, end);
            expressWaybillRepository.batchInsert(subList);
            log.info("已插入第{}批，共{}条面单数据", i + 1, subList.size());
        }
    }


    @Override
    public CancelExpressRespVO cancel(CancelExpressReqVO cancelExpressReqVO) {
        if (StrUtil.isBlank(cancelExpressReqVO.getWaybillNo())) {
            throw new BizException("waybillNo must not be null.");
        }
        // 通过运单号获取 快递对接订单号
        Optional<ExpressOrderPO> expressOrderPOOptional = expressOrderRepository.selectWaybillNo(cancelExpressReqVO.getWaybillNo());
        if (!expressOrderPOOptional.isPresent()) {
            throw new BizException("未找到快递单号！");
        }
        if (expressOrderPOOptional.get().getStatus().equals(ExpressOrderStatusEnum.CANCELLED.getCode())) {
            throw new BizException("快递单已取消！");
        }

        cancelExpressReqVO.setExpressOrderNo(expressOrderPOOptional.get().getExpressOrderNo());

        AbstractSfApiTemplate handler = sfApiHandlerFactory.getHandler(SfApiCodeEnum.CANCEL_ORDER);
        CancelExpressRespVO cancelExpressRespVO = (CancelExpressRespVO) handler.execute(cancelExpressReqVO);

        ExpressOrderPO expressOrderPO = expressOrderPOOptional.get();
        expressOrderPO.setStatus(ExpressOrderStatusEnum.CANCELLED.getCode());
        expressOrderRepository.update(expressOrderPO);

        return cancelExpressRespVO;
    }


    @Override
    public UpdateExpressRespVO update(UpdateExpressReqVO updateExpressReqVO) {
        StringUtil.removeSpaces(updateExpressReqVO);
        ExpressValidation.updateVerify(updateExpressReqVO);
        //todo 通过运单号获取 快递对接订单号
        updateExpressReqVO.setOrderNo("OD3493843984934_1756373683802");
        UpdateExpressRespVO updateExpressRespVO = (UpdateExpressRespVO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.UPDATE_ORDER).execute(updateExpressReqVO);
        return updateExpressRespVO;
    }

    @Override
    public SearchRoutesRespDTO searchRoutes(SearchRoutesReqVO reqVO) {
        @SuppressWarnings("unchecked")
        SearchRoutesRespDTO routesRespDTO = (SearchRoutesRespDTO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.SEARCH_ROUTES).execute(reqVO);
        return routesRespDTO;
    }

    @Override
    public List<SearchSingleRouteRespVO.SearchSingleRouteElem> querySingleRoute(SearchSingleRouteReqVO searchSingleRouteReqVO) {
        List<ExpressOrderTrackPO> trackPOs = trackRepository.selectByWaybillNo(searchSingleRouteReqVO.getWaybillNo());
        return trackPOs.stream().map(t ->
                new SearchSingleRouteRespVO.SearchSingleRouteElem()
                        .setWaybillNo(t.getWaybillNo())
                        .setEvent(ExpressOrderStatusEnum.getByCode(t.getOpCodeMap()).getDesc())
                        .setEventTime(t.getTrackTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        .setRemark(t.getRemark())).collect(Collectors.toList());
    }


}
