package com.qudian.idle.bifrost.application.factory.sfExpress.trace.inputProcessor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.qudian.idle.bifrost.api.enums.trace.TraceSourceEnum;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.RouteProcessingContext;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.bifrost.application.factory.sfExpress.trace.inputProcessor.IsfTraceInputProcessor</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/29
 */
public interface IsfTraceInputProcessor<T> {
    /**
     * 解析轨迹输入数据，填充到统一上下文
     */
    void parseInput(RouteProcessingContext context, T input);

    /**
     * 获取输入类型标识
     */
    TraceSourceEnum source();

    /**
     * 轨迹以JSON输入
     *
     * <AUTHOR>
     * @date 2025/08/29
     */
    @Slf4j
    class SfTraceJSONInputProcessor implements IsfTraceInputProcessor<String> {
        @Override
        public void parseInput(RouteProcessingContext context, String routeJSON) {
            if (StringUtils.isBlank(routeJSON)) {
                context.withErrorMetaData("S(JSON_INPUT);E(json is empty)");
                throw new IllegalArgumentException("顺丰轨迹数据[JSON]不能为空");
            }
            try {
                SfExpressRouteDTO parsed = JSON.parseObject(routeJSON, SfExpressRouteDTO.class);
                if (CollectionUtil.isEmpty(parsed.getWaybillRoute())) {
                    context.withErrorMetaData("S(JSON_INPUT);E(waybillRoute is empty)");
                    throw new IllegalArgumentException("顺丰轨迹数据[waybillRoute]不能为空");
                }
                //转换为Map<String /* waybillNo */, SfExpressRouteDTO>
                Map<String, SfExpressRouteDTO> parsedData = parsed.getWaybillRoute().stream()
                        .collect(Collectors.groupingBy(SfExpressRouteDTO.SfExpressRouteDetailDTO::getWaybillNo))
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> new SfExpressRouteDTO(entry.getValue(), this.source())));
                if (MapUtil.isEmpty(parsedData)) {
                    context.withErrorMetaData("S(JSON_INPUT);E(parsedData is empty)");
                    throw new IllegalArgumentException("顺丰轨迹数据[parsedData]不能为空");
                }
                context.setParsedData(parsedData)
                        .setBodyJSON(routeJSON)
                        .withMetadata(RouteProcessingContext.TraceMetadata.VALIDATION_STATUS, "PASSED")
                        .withMetadata(RouteProcessingContext.TraceMetadata.ROUTE_COUNT, parsed.getWaybillRoute().size());
            } catch (Exception e) {
                log.error("[sf_trace]SfTraceJSONInputProcessor.解析顺丰轨迹数据失败: {}", Throwables.getStackTraceAsString(e), e);
                if (e instanceof IllegalArgumentException) {
                    throw e;
                }
                throw new BizException("[sf_trace]解析顺丰轨迹数据失败");
            }
        }

        @Override
        public TraceSourceEnum source() {
            return TraceSourceEnum.PULL;
        }
    }

    /**
     * 轨迹回调
     *
     * <AUTHOR>
     * @date 2025/08/29
     */
    class SfTraceCallbackInputProcessor implements IsfTraceInputProcessor<SfExpressRouteDTO> {
        @Override
        public void parseInput(RouteProcessingContext context, SfExpressRouteDTO routeDTO) {
            if (Objects.isNull(routeDTO)) {
                context.withErrorMetaData("S(CALLBACK_INPUT);E(routeDTO is null)");
                throw new IllegalArgumentException("顺丰轨迹数据[SfExpressRouteDTO]不能为空");
            }
            if (CollectionUtil.isEmpty(routeDTO.getWaybillRoute())) {
                context.withErrorMetaData("S(CALLBACK_INPUT);E(waybillRoute is empty)");
                throw new IllegalArgumentException("顺丰轨迹数据[waybillRoute]不能为空");
            }
            //转换为Map<String /* waybillNo */, SfExpressRouteDTO>
            Map<String, SfExpressRouteDTO> parsedData = routeDTO.getWaybillRoute().stream()
                    .collect(Collectors.groupingBy(SfExpressRouteDTO.SfExpressRouteDetailDTO::getWaybillNo))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> new SfExpressRouteDTO(entry.getValue(), this.source())));
            if (MapUtil.isEmpty(parsedData)) {
                context.withErrorMetaData("S(CALLBACK_INPUT);E(parsedData is empty)");
                throw new IllegalArgumentException("顺丰轨迹数据[parsedData]不能为空");
            }
            context.setParsedData(parsedData)
                    .setBodyJSON(JSON.toJSONString(routeDTO))
                    .withMetadata(RouteProcessingContext.TraceMetadata.VALIDATION_STATUS, "PASSED")
                    .withMetadata(RouteProcessingContext.TraceMetadata.ROUTE_COUNT, routeDTO.getWaybillRoute().size());
        }

        @Override
        public TraceSourceEnum source() {
            return TraceSourceEnum.PUSH;
        }
    }


    /**
     * 主动发起轨迹查询
     *
     * <AUTHOR>
     * @date 2025/08/29
     */
    class SfTraceQueryInputProcessor implements IsfTraceInputProcessor<SearchRoutesRespDTO> {
        @Override
        public void parseInput(RouteProcessingContext context, SearchRoutesRespDTO searchRoutesDTO) {
            if (Objects.isNull(searchRoutesDTO) || CollectionUtil.isEmpty(searchRoutesDTO.getMsgData().getRouteResps())) {
                context.withErrorMetaData("S(QUERY_INPUT);E(searchRoutesDTO is empty)");
                throw new IllegalArgumentException("顺丰搜索轨迹数据不能为空");
            }
            Map<String, SfExpressRouteDTO> parsedData = convertSearchRoutesToExpressRoute(searchRoutesDTO);
            if (MapUtil.isEmpty(parsedData)) {
                context.withErrorMetaData("S(QUERY_INPUT);E(parsedData is empty)");
                throw new IllegalArgumentException("顺丰搜索轨迹数据转换失败");
            }
            context.setParsedData(parsedData)
                    .setBodyJSON(JSON.toJSONString(searchRoutesDTO))
                    .withMetadata(RouteProcessingContext.TraceMetadata.VALIDATION_STATUS, "PASSED")
                    .withMetadata(RouteProcessingContext.TraceMetadata.ROUTE_COUNT, parsedData.values().stream().mapToInt(route -> route.getWaybillRoute().size()).sum());
        }

        /**
         * 将顺丰路由，转换为业务域自身路由
         *
         * @param searchRoutesDTO
         * @return {@link Map }<{@link String }, {@link SfExpressRouteDTO }>
         */
        private Map<String, SfExpressRouteDTO> convertSearchRoutesToExpressRoute(SearchRoutesRespDTO searchRoutesDTO) {
            if (Objects.isNull(searchRoutesDTO) || CollectionUtil.isEmpty(searchRoutesDTO.getMsgData().getRouteResps())) {
                return Collections.emptyMap();
            }
            List<SearchRoutesRespDTO.SearchRoutesDataResp> dataRespList = searchRoutesDTO.getMsgData().getRouteResps();
            return dataRespList.stream()
                    .collect(Collectors.groupingBy(SearchRoutesRespDTO.SearchRoutesDataResp::getMailno))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            entry -> {
                                List<SfExpressRouteDTO.SfExpressRouteDetailDTO> routeDetails = entry.getValue().stream()
                                        .flatMap(route -> route.getRoutes().stream())
                                        .map(routeData ->
                                                new SfExpressRouteDTO.SfExpressRouteDetailDTO()
                                                        .setWaybillNo(entry.getKey())
                                                        .setAcceptAddress(routeData.getAcceptAddress())
                                                        .setAcceptTime(routeData.getAcceptTime())
                                                        .setRemark(routeData.getRemark())
                                                        .setOpCode(routeData.getOpCode())
                                        )
                                        .collect(Collectors.toList());
                                return new SfExpressRouteDTO(routeDetails, this.source());
                            }));
        }

        @Override
        public TraceSourceEnum source() {
            return TraceSourceEnum.PULL;
        }
    }
}
