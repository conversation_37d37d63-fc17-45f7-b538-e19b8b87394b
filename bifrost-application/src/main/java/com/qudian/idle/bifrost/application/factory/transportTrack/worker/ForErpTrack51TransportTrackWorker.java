package com.qudian.idle.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.application.convertor.Track51TransportTrackStruct;
import com.qudian.idle.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.idle.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.idle.bifrost.infrastructure.repository.remote.Track51RemoteService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.track51.Track51TransportRequestVO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/16
 **/
@Component
@Slf4j
public class ForErpTrack51TransportTrackWorker extends BaseTransportTrackWorker {
    @Resource
    private Track51RemoteService track51RemoteService;
    @Resource
    private Track51TransportTrackStruct track51TransportTrackStruct;
    @Value("${track51.track.partition.size:10}")
    private Integer partitionSize;

    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }

    /**
     * 51track物流状态转换为自定义状态
     */
    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("pending", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("inforeceived", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("transit", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("pickup", TransshipmentStatusEnum.AWAITING_COLLECTION)
        // 转运已签收
        .put("delivered", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("notfound", TransshipmentStatusEnum.ABNORMAL)
        .put("expired", TransshipmentStatusEnum.ABNORMAL)
        .put("undelivered", TransshipmentStatusEnum.ABNORMAL)
        .put("exception", TransshipmentStatusEnum.ABNORMAL)
        .build();

    @Override
    public String support() {
        // 菜鸟线下转运，临时字段
        return Strings.EMPTY;
    }

    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        // usps查询接口每批10个
        return (orderTransportPOList -> Lists.partition(orderTransportPOList, partitionSize).stream()
            .map(this::queryTrack)
            .flatMap(Collection::stream)
            .collect(Collectors.toList()));
    }

    private List<TransportTrackRespVO.TrackingResult> queryTrack(List<OrderTransportPO> orderTransportPOList) {
        Map<String, String> articaleToOrderMap = orderTransportPOList.stream()
            .collect(Collectors.toMap(OrderTransportPO::getArticleId, OrderTransportPO::getOrderNumber,
                (o1, o2) -> o1));
        List<String> orderList = new ArrayList<>(articaleToOrderMap.values());
        List<String> articleIdList = new ArrayList<>(articaleToOrderMap.keySet());
        Track51TransportRequestVO requestVO = Track51TransportRequestVO
            .builder()
            .transportNos(articleIdList)
            .build();
        // 承运商转换为wiseway
        return track51RemoteService.getAusPostData(requestVO,orderList).stream()
            .map(v-> track51TransportTrackStruct.toVOResult(v, articaleToOrderMap))
            .collect(Collectors.toList());
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1))
        );
    }

    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        // 兜底返回未知
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(result.getStatus().toLowerCase(), TransshipmentStatusEnum.UNKNOWN).getCode();
    }
}
