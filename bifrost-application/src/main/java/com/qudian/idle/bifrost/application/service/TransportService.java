package com.qudian.idle.bifrost.application.service;

import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.vo.PagingList;
import com.qudian.idle.bifrost.api.vo.request.*;
import com.qudian.idle.bifrost.api.vo.response.DeliverySettleListResVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;

import javax.validation.Valid;

public interface TransportService {
    BaseResponseDTO<String> cancelTransport(@Valid CancelTransportReqVO reqVO);

    BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(@Valid PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);

    BaseResponseDTO<String> dispatchSingleTransport(SingleOrderTransportReqVO reqVO);

    PagingList<DeliverySettleListResVO> querySettleList(DeliverySettleListReqVO reqVO);
}
