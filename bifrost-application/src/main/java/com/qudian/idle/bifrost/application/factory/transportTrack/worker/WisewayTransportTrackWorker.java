package com.qudian.idle.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.application.convertor.WiseWayTransportTrackStruct;
import com.qudian.idle.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.idle.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.idle.bifrost.infrastructure.repository.remote.WiseWayService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackReqVO;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/13
 **/
@Component
@Slf4j
public class WisewayTransportTrackWorker extends BaseTransportTrackWorker {

    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("40000", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("40100", TransshipmentStatusEnum.IN_TRANSIT)
        .put("40200", TransshipmentStatusEnum.IN_TRANSIT)
        .put("40300", TransshipmentStatusEnum.IN_TRANSIT)
        .put("40400", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("40500", TransshipmentStatusEnum.AWAITING_COLLECTION)
        // 转运已签收
        .put("40600", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("40700", TransshipmentStatusEnum.ABNORMAL)
        .build();
    @Resource
    private WiseWayService wiseWayService;
    @Resource
    private WiseWayTransportTrackStruct wiseWayTransportTrackStruct;
    @Value("${wiseway.track.partition.size:10}")
    private Integer partitionSize;

    @Override
    public String support() {
        return CarrierTypeEnum.AUS_POST_WISE_WAY.getName();
    }

    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }

    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        return (orderTransportPOList -> Lists.partition(orderTransportPOList, partitionSize).stream()
            .map(this::getTrackingResults)
            .flatMap(Collection::stream)
            .collect(Collectors.toList()));
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1)));
    }

    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        String eventCode = result.getEvents().get(0).getEventCode();
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(eventCode, TransshipmentStatusEnum.UNKNOWN).getCode();
    }

    @NotNull
    private List<TransportTrackRespVO.TrackingResult> getTrackingResults(List<OrderTransportPO> orderTransportPOList) {
        Map<String, String> articaleToOrderMap = orderTransportPOList.stream()
            .collect(Collectors.toMap(OrderTransportPO::getArticleId, OrderTransportPO::getOrderNumber,
                (o1, o2) -> o1));
        List<String> orderList = new ArrayList<>(articaleToOrderMap.values());
        List<String> articleIdList = new ArrayList<>(articaleToOrderMap.keySet());
        WiseWayOrderTrackReqVO reqDTO = new WiseWayOrderTrackReqVO();
        reqDTO.setTrackNums(articleIdList);
        reqDTO.setOrderNums(orderList);
        return wiseWayService.queryOrderTrack(reqDTO).stream()
            .map(dto -> wiseWayTransportTrackStruct.toTrackResult(dto, articaleToOrderMap))
            .collect(Collectors.toList());
    }
}
