package com.qudian.idle.bifrost.application.service;

import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CancelExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchSingleRouteReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CancelExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.UpdateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.trace.SearchSingleRouteRespVO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
public interface ExpressService {
    CreateExpressRespVO create(CreateExpressReqVO createExpressReqVO);

    BatchPrintRespVO batchPrint(BatchPrintReqVO batchPrintReqVO);

    CancelExpressRespVO cancel(CancelExpressReqVO batchCancelReqVO);

    UpdateExpressRespVO update(UpdateExpressReqVO updateExpressReqVO);

    SearchRoutesRespDTO searchRoutes(SearchRoutesReqVO reqVO);

    List<SearchSingleRouteRespVO.SearchSingleRouteElem> querySingleRoute(SearchSingleRouteReqVO searchSingleRouteReqVO);

    List<SearchExpressRespVO> searchExpress(SearchExpressReqVO searchExpressReqVO);
}
