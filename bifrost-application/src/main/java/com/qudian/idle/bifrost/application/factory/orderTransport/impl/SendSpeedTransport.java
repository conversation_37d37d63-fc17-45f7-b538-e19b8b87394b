package com.qudian.idle.bifrost.application.factory.orderTransport.impl;

import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.application.factory.orderTransport.OrderTransportHandler;
import com.qudian.idle.bifrost.infrastructure.repository.remote.SendSpeedRemoteService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;


//@Component
public class SendSpeedTransport extends OrderTransportHandler {
    @Resource
    private SendSpeedRemoteService sendSpeedRemoteService;
    @Override
    public String support() {
        return CarrierTypeEnum.USPS.getName();
    }

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        // 取消转运订单
        sendSpeedRemoteService.cancelTransport(reqVO);
    }

    @Override
    public PushSingleTransportResponseVO pushSingleTransport(PushSingleTransportReqVO reqVO) {
        PushSingleTransportResponseVO pushSingleTransportResponseVO = sendSpeedRemoteService.createOrder(reqVO);
        if (Objects.nonNull(pushSingleTransportResponseVO)){
            pushSingleTransportResponseVO.setCarrier(CarrierTypeEnum.USPS.getName());
            pushSingleTransportResponseVO.setForwarderType(ForwarderTypeEnum.SPEED.getName());
        }
        return pushSingleTransportResponseVO;
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(@Valid SingleOrderTransportReqVO reqVO) {
        return sendSpeedRemoteService.printSingleLabel(reqVO);
    }

    @Override
    public String dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        return "transfer initiate success";
    }
}
