package com.qudian.idle.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.idle.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.idle.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.idle.bifrost.application.convertor.EweTransportTrackStruct;
import com.qudian.idle.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.idle.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.idle.bifrost.infrastructure.repository.remote.EweRemoteService;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@inheritDoc} ewe转运轨迹同步处理
 *
 * <AUTHOR>
 * @since 2023/8/8
 **/
@Component
public class EweTransportTrackWorker extends BaseTransportTrackWorker {

    /**
     * 澳邮状态转换到自定转运状态
     */
    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("created", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("sealed", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("initiated", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("unsuccessful pickup", TransshipmentStatusEnum.WAITING_PICKUP)
        .put("shipment information received", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("in transit", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("awaiting collection", TransshipmentStatusEnum.AWAITING_COLLECTION)
        // 转运已签收
        .put("delivered", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("article damaged", TransshipmentStatusEnum.ABNORMAL)
        .put("held by courier", TransshipmentStatusEnum.ABNORMAL)
        .put("cannot be delivered", TransshipmentStatusEnum.ABNORMAL)
        .put("track items for detailed delivery information", TransshipmentStatusEnum.ABNORMAL)
        .put("possible delay", TransshipmentStatusEnum.ABNORMAL)
        .build();
    @Resource
    private EweRemoteService eweRemoteService;
    @Resource
    private EweTransportTrackStruct eweTransportTrackStruct;

    @Value("${ewe.track.partition.size:10}")
    private Integer partitionSize;


    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }

    @Override
    public String support() {
        return CarrierTypeEnum.AUS_POST.getName();
    }

    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        return orderTransportPOList -> {
            // 根据州分组 改为根据州和channel分组
            Map<Optional<String>, List<OrderTransportPO>> byState = orderTransportPOList.stream()
                .collect(Collectors.groupingBy(v -> Optional.ofNullable(v.getState())));
            List<TransportTrackRespVO.TrackingResult> res = new ArrayList<>();
            byState.forEach((state, orderTransports) -> {
                // 按照partition分区
                Lists.partition(orderTransports, partitionSize)
                    .forEach(list -> res.addAll(queryTrackFunctionByState().apply(list, state.orElse(null))));
            });
            return res;
        };
    }

    private BiFunction<List<OrderTransportPO>, String, List<TransportTrackRespVO.TrackingResult>> queryTrackFunctionByState() {
        List<TransportTrackRespVO.TrackingResult> res = new ArrayList<>();
        return (orderTransportPOList, state) -> {
            // 这个时候根据channel group
            Map<Optional<String>, List<OrderTransportPO>> channelMap = orderTransportPOList.stream()
                    .collect(Collectors.groupingBy(v -> Optional.ofNullable(v.getOrigin())));

            channelMap.forEach((k, v) -> {
                List<String> orderNumbers = v.stream()
                        .map(OrderTransportPO::getOrderNumber)
                        .collect(Collectors.toList());
                TransportTrackReqVO reqVO = TransportTrackReqVO.builder()
                        .orderNumberList(orderNumbers)
                        .state(state)
                        .origin(k)
                        .build();
                res.addAll(eweTransportTrackStruct.toVO(eweRemoteService.shippingStatus(reqVO)).getTrackingResults().stream()
                        // 留下查询成功的
                        .filter(trackingResult -> Objects.nonNull(trackingResult.getEvents()))
                        .collect(Collectors.toList()));
            });
            return res;
        };
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1))
        );
    }


    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        // 兜底返回未知
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(result.getStatus().toLowerCase(), TransshipmentStatusEnum.UNKNOWN).getCode();
    }


}
