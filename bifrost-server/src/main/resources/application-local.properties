management.endpoints.web.exposure.include=info,health
logging.config=classpath:log4j2-spring-local.xml
logging.level.com.qudian.wanwu.shop.tripod.dao=debug
apollo.bootstrap.namespaces=application,database,dubbo,redis
apollo.bootstrap.enabled=false

logging.level.com.qudian.idle.bifrost.infrastructure.repository.database.mapper=debug

spring.messages.encoding=UTF-8
spring.jpa.show-sql = false
hibernate.enable_lazy_load_no_trans = true

spring.datasource.url = ************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username = qdfresh_test
spring.datasource.password = ZYF5gThaUNdf4QV6
spring.datasource.type = com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.jdbc-url = ************************************************************************************************************************************************************************************************************************************************************
#spring.datasource.driver.jdbc-url = ************************************************************************************************************************************************************************************************************************************************************
#spring.datasource.driver.username = qdfresh_test
#spring.datasource.driver.password = ZYF5gThaUNdf4QV6
#spring.datasource.driver.type = com.zaxxer.hikari.HikariDataSource
#spring.datasource.driver.driver-class-name = com.mysql.cj.jdbc.Driver

spring.redis.host = lme-dev1-o.redis.zhangbei.rds.aliyuncs.com
spring.redis.block-when-exhausted = false
spring.redis.database = 12
spring.redis.port = 6379
spring.redis.jedis.pool.max-active = 200
spring.redis.jedis.pool.max-wait = 10000
spring.redis.jedis.pool.max-idle = 8
spring.redis.jedis.pool.min-idle = 0
spring.redis.timeout = 10000
spring.cache.redis.time-to-live = 600
spring.redis.password = JQgFNrilA86vGRjh


dubbo.protocol.dubbo.payload = 8388608
dubbo.application.name = idle.bifrost.srv.provider
dubbo.registry.address = zookeeper://127.0.0.1:2181
#dubbo.registry.address = zookeeper://*************:32181
dubbo.registry.timeout = 6000
dubbo.metadata-report.address = zookeeper://127.0.0.1:2181
dubbo.protocol.name = dubbo
dubbo.protocol.port = 21005
dubbo.scan.base-packages = com.qudian.*
dubbo.reference.check = false
dubbo.consumer.check = false
dubbo.consumer.timeout = 5000
dubbo.consumer.retries = 0
dubbo.registry.check = false
dubbo.provider.threads = 200
dubbo.provider.retries = 0
dubbo.provider.version = 1.0.0
dubbo.refrence.defalut.verion = 1.0.0
dubbo.provider.validation = true
dubbo.provider.filter = -exception,-validation,generic,dubboExceptionFilter,dubboValidationFilter,default,dubboI18nFilter
dubbo.consumer.parameters.local-invasive-enable = true
dubbo.consumer.parameters.test01.ip = **************
dubbo.consumer.parameters.send.reconnect=true

#MQ
rocketmq.name-server = http://mq-qa.quplusplus.net:9876
rocketmq.producer.group = GID_LME_BIFROST_test
rocketmq.producer.topic = TOPIC_BIFROST_test

#JOB
xxl.job.admin.addresses = http://xxl-job.qa.fadongxi.com/xxl-job-admin/
xxl.job.executor.appname = lme-bifrost-local
xxl.job.executor.ip =
xxl.job.executor.port = 8104
xxl.job.accessToken =
xxl.job.executor.logpath = logs/xxl-job/jobhandler
xxl.job.executor.logretentiondays = 30

#rate limit
lme.rate.limit.config.ewe.per-seconds=1
lme.rate.limit.config.ewe.limit=1

#Business
lme.tool.host = http://lme-dev-tool-srv.quplusplus.net
country.code = au

lme.aus.transport.host = https://jerrytest.ewe.com.au/eweApi/ewe
lme.aus.transport.username = dl-syd1001
lme.aus.transport.password = pso0KOVa
lme.aus.transport.pushUnCreatedShipments = /api/pushShipment
lme.aus.transport.timezone = UTC
lme.aus.transport.shipping-status = /aupost/trackArticle
lme.aus.transport.dispatchShipments = /api/dispatchShipments
lme.aus.transport.cancelShipment = /api/cancelShipment
lme.aus.transport.modifyShipment = /api/modifyShipment
lme.aus.transport.printLabel = /api/createAllLabel

  


lms.wisway.transport.appkey=wiseway
lms.wisway.transport.secret=cxfk17xo7aKSS5722lDM_w
lms.wisway.transport.host=https://wisecomm.wiseway.com.au
lms.wisway.state.mapping =[{"state":"VIC","targetWarehouseLocationState":"MEL"},{"state":"NSW",","targetWarehouseLocationState":"SYD"},{"state":"SDW","targetWarehouseLocationState":"PER"},{"state":"BNE","targetWarehouseLocationState":"BNE"},{"state":"ADL","targetWarehouseLocationState":"ADL"}]  
transport.track.query.order.partition.size = 100
transport.track.task.partition.size = 10
transport.track.task.query.remote.retry.time = 3
transport.track.query.batch.size = 10

push.order.block.channels  = ewe,usps

aus.transport.account.info = [{"state":"VIC","username":"dl-syd1001","password":"pso0KOVa","targetWarehouseLocationState":"MEL"},{"state":"NSW","username":"FHEXPRESS","password":"5llwX8Hy","targetWarehouseLocationState":"SYD"},{"state":"SDW","username":"FHEXPRESS-PER","password":"9WLMWlGIJ","targetWarehouseLocationState":"PER"},{"state":"BNE","username":"FHEXPRESS-BNE","password":"F9KWoE4T","targetWarehouseLocationState":"BNE"},{"state":"ADL","username":"FHEXPRESS-ADL","password":"5gKVTk49","targetWarehouseLocationState":"ADL"}]
aus.transport.special.account.info=[{"origin":"YourBox","accountList":[{"state":"VIC","username":"dl-syd1001","password":"pso0KOVabbb","targetWarehouseLocationState":"MEL"}]}]
# ????
performance.channels=["YourBox"]
swagger.dubbo.enable = true

performance.Topic=TOPIC_BIFROST_PERFORMANCE_DEV

gls.uid = 6BAB7A53-3B6D-4D5A-9450-702D2FAC0B11
