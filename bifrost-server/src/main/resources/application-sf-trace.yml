# 顺丰轨迹并发处理配置
sf:
  trace:
    # 是否启用并发处理
    concurrent-enabled: true
    
    # 线程池配置
    thread-pool:
      core-size: 10
      max-size: 20
      keep-alive-seconds: 60
      queue-capacity: 1000
      task-timeout-seconds: 30
    
    # 流控配置
    rate-limit:
      max-calls-per-second: 30.0
      acquire-timeout-seconds: 10
      warmup-enabled: true
      warmup-seconds: 2
    
    # 批处理配置
    batch:
      size: 10
      page-size: 100
    
    # 弹性配置
    resilience:
      circuit-breaker:
        failure-rate-threshold: 50.0
        wait-duration-seconds: 30
        sliding-window-size: 20
        minimum-number-of-calls: 10
      retry:
        max-attempts: 3
        wait-duration-seconds: 1
        exponential-backoff-multiplier: 2

# Resilience4j配置
resilience4j:
  circuitbreaker:
    instances:
      sfApiCircuitBreaker:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 20
        minimum-number-of-calls: 10
        slow-call-rate-threshold: 80
        slow-call-duration-threshold: 3s
        permitted-number-of-calls-in-half-open-state: 5
        automatic-transition-from-open-to-half-open-enabled: true
  
  retry:
    instances:
      sfApiRetry:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.lang.RuntimeException
          - java.util.concurrent.TimeoutException
        ignore-exceptions:
          - java.lang.IllegalArgumentException

# Micrometer监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: bifrost-sf-trace
      service: trace-processor

# 日志配置
logging:
  level:
    com.qudian.idle.bifrost.server.job.trace.concurrent: DEBUG
    com.qudian.idle.bifrost.server.controller: INFO
    io.github.resilience4j: DEBUG
    com.google.common.util.concurrent: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
