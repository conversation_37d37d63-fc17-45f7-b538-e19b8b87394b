#spring
spring.application.name=idle-bifrost-server
server.servlet.context-path=/api
spring.profiles.active=@profilesActive@
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
#dubbo
dubbo.protocol.accesslog=true
dubbo.application.logger=log4j

server.port=61005
#apollo
app.id=idle-bifrost-srv

rocketmq.consumer[0].topic=WMS_EVENT_ORDER_STATE_${spring.profiles.active}
rocketmq.consumer[0].selector-expression=op_upload_transport
rocketmq.consumer[0].group=CAi_NIAO_TRANSPORT_${spring.profiles.active}

rocketmq.consumer[1].topic=TOPIC_BIFROST_TRACK_QUERY_NUMBER_MAPPING_PROD
rocketmq.consumer[1].group=TRACK_QUERY_NUMBER_MAPPING_${spring.profiles.active}
