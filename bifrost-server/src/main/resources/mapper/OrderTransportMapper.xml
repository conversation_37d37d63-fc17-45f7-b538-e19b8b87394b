<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace:填写映射当前的Mapper接口，所有的增删改查的参数和返回值类型，
		就可以直接填写缩写，不区分大小写，直接通过方法名去找类型-->
<mapper namespace="com.qudian.idle.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper">

    <select id="queryOrderTransportOne"
            resultType="com.qudian.idle.bifrost.infrastructure.repository.database.po.OrderTransportPO">
        SELECT * FROM bifrost_order_transport WHERE order_number = #{orderNumber}
        <if test="deleteFlag != null">
            AND delete_flag = #{deleteFlag}
        </if>
        order by created_time desc LIMIT 1
    </select>
</mapper>
