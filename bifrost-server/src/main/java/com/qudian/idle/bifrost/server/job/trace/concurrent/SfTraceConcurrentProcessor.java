package com.qudian.idle.bifrost.server.job.trace.concurrent;

import cn.hutool.core.collection.CollectionUtil;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 基于CompletableFuture的顺丰轨迹并发处理器
 * 优雅的异步并发处理实现
 * 
 * <AUTHOR>
 * @since 2025/09/02
 */
@Slf4j
@Component
public class SfTraceConcurrentProcessor {
    
    @Resource
    private ExpressService expressService;
    
    @Resource
    private SfExpressTracePipeline tracePipeline;
    
    @Resource
    private SfTraceRateLimiter rateLimiter;
    
    /**
     * 线程池配置
     */
    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = 20;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final int QUEUE_CAPACITY = 1000;
    private static final int BATCH_SIZE = 10;
    private static final int TASK_TIMEOUT_SECONDS = 30;
    
    /**
     * 自定义线程池
     */
    private ThreadPoolExecutor executor;
    
    /**
     * 任务计数器
     */
    private final AtomicInteger taskCounter = new AtomicInteger(0);
    
    @PostConstruct
    public void init() {
        // 创建优雅的线程池
        this.executor = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            this::createThread,
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 预热流控器
        rateLimiter.warmUp(Duration.ofSeconds(2));
        
        log.info("顺丰轨迹并发处理器初始化完成 - 核心线程: {}, 最大线程: {}, 队列容量: {}", 
                CORE_POOL_SIZE, MAX_POOL_SIZE, QUEUE_CAPACITY);
    }
    
    /**
     * 优雅的并发处理入口
     * 
     * @param expressOrders 快递订单列表
     * @return 处理结果
     */
    public ProcessResult processConcurrently(List<ExpressOrderPO> expressOrders) {
        if (CollectionUtil.isEmpty(expressOrders)) {
            return ProcessResult.empty();
        }
        
        log.info("开始并发处理顺丰轨迹查询，订单数量: {}", expressOrders.size());
        
        // 分批处理
        List<List<ExpressOrderPO>> batches = partitionList(expressOrders, BATCH_SIZE);
        
        // 创建CompletableFuture任务流
        List<CompletableFuture<BatchResult>> futures = batches.stream()
            .map(this::createBatchTask)
            .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        return collectResults(futures, expressOrders.size());
    }
    
    /**
     * 创建批次处理任务
     */
    private CompletableFuture<BatchResult> createBatchTask(List<ExpressOrderPO> batch) {
        return CompletableFuture
            .supplyAsync(() -> processBatch(batch), executor)
            //TODO@chr.任务超时处理
            //.orTimeout(TASK_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .exceptionally(throwable -> handleBatchException(batch, throwable));
    }
    
    /**
     * 处理单个批次
     */
    private BatchResult processBatch(List<ExpressOrderPO> batch) {
        int taskId = taskCounter.incrementAndGet();
        String threadName = Thread.currentThread().getName();
        
        log.debug("任务[{}]开始处理批次，线程: {}, 订单数量: {}", taskId, threadName, batch.size());
        
        try {
            // 优雅的流控获取
            if (!rateLimiter.tryAcquire(Duration.ofSeconds(10))) {
                log.warn("任务[{}]获取API调用许可超时", taskId);
                return BatchResult.failed(batch.size());
            }
            
            // 执行API调用
            List<String> waybillNos = extractWaybillNos(batch);
            SearchRoutesRespDTO response = callSfApi(waybillNos);
            
            // 处理响应
            tracePipeline.flowByQuery(response);
            
            log.debug("任务[{}]处理完成，成功处理{}个订单", taskId, batch.size());
            return BatchResult.success(batch.size());
            
        } catch (Exception e) {
            log.error("任务[{}]处理异常", taskId, e);
            return BatchResult.failed(batch.size());
        }
    }
    
    /**
     * 调用顺丰API
     */
    private SearchRoutesRespDTO callSfApi(List<String> waybillNos) {
        return expressService.searchRoutes(new SearchRoutesReqVO().setWaybillNo(waybillNos));
    }
    
    /**
     * 提取运单号
     */
    private List<String> extractWaybillNos(List<ExpressOrderPO> batch) {
        return batch.stream()
            .map(ExpressOrderPO::getWaybillNo)
            .collect(Collectors.toList());
    }
    
    /**
     * 处理批次异常
     */
    private BatchResult handleBatchException(List<ExpressOrderPO> batch, Throwable throwable) {
        if (throwable instanceof TimeoutException) {
            log.error("批次处理超时，订单数量: {}", batch.size());
        } else {
            log.error("批次处理异常，订单数量: {}", batch.size(), throwable);
        }
        return BatchResult.failed(batch.size());
    }
    
    /**
     * 收集所有任务结果
     */
    private ProcessResult collectResults(List<CompletableFuture<BatchResult>> futures, int totalOrders) {
        try {
            // 等待所有任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );
            
            allTasks.get(TASK_TIMEOUT_SECONDS * futures.size(), TimeUnit.SECONDS);
            
            // 统计结果
            int totalSuccess = 0;
            int totalFailed = 0;
            
            for (CompletableFuture<BatchResult> future : futures) {
                BatchResult result = future.get();
                totalSuccess += result.successCount;
                totalFailed += result.failedCount;
            }
            
            ProcessResult result = new ProcessResult(totalOrders, totalSuccess, totalFailed);
            log.info("并发处理完成: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("收集处理结果异常", e);
            futures.forEach(future -> future.cancel(true));
            return ProcessResult.allFailed(totalOrders);
        }
    }
    
    /**
     * 优雅的列表分割
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        return list.stream()
            .collect(Collectors.groupingBy(item -> list.indexOf(item) / batchSize))
            .values()
            .stream()
            .collect(Collectors.toList());
    }
    
    /**
     * 创建线程
     */
    private Thread createThread(Runnable r) {
        Thread thread = new Thread(r, "sf-trace-worker-" + taskCounter.incrementAndGet());
        thread.setDaemon(false);
        thread.setUncaughtExceptionHandler((t, e) -> 
            log.error("线程{}发生未捕获异常", t.getName(), e));
        return thread;
    }
    
    /**
     * 获取处理器状态
     */
    public ProcessorStatus getStatus() {
        return new ProcessorStatus(
            executor.getCorePoolSize(),
            executor.getMaximumPoolSize(),
            executor.getActiveCount(),
            executor.getPoolSize(),
            executor.getQueue().size(),
            executor.getCompletedTaskCount(),
            rateLimiter.getRate()
        );
    }
    
    /**
     * 优雅关闭
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始优雅关闭顺丰轨迹并发处理器");
        executor.shutdown();
        try {
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("处理器未能在30秒内正常关闭，强制关闭");
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executor.shutdownNow();
        }
        log.info("顺丰轨迹并发处理器已关闭");
    }
    
    /**
     * 批次处理结果
     */
    public static class BatchResult {
        public final int successCount;
        public final int failedCount;
        
        private BatchResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        public static BatchResult success(int count) {
            return new BatchResult(count, 0);
        }
        
        public static BatchResult failed(int count) {
            return new BatchResult(0, count);
        }
    }
    
    /**
     * 处理结果
     */
    public static class ProcessResult {
        public final int totalProcessed;
        public final int totalSuccess;
        public final int totalFailed;
        public final double successRate;
        
        public ProcessResult(int totalProcessed, int totalSuccess, int totalFailed) {
            this.totalProcessed = totalProcessed;
            this.totalSuccess = totalSuccess;
            this.totalFailed = totalFailed;
            this.successRate = totalProcessed > 0 ? (totalSuccess * 100.0 / totalProcessed) : 0;
        }
        
        public static ProcessResult empty() {
            return new ProcessResult(0, 0, 0);
        }
        
        public static ProcessResult allFailed(int total) {
            return new ProcessResult(total, 0, total);
        }
        
        @Override
        public String toString() {
            return String.format("ProcessResult{总处理=%d, 成功=%d, 失败=%d, 成功率=%.1f%%}", 
                               totalProcessed, totalSuccess, totalFailed, successRate);
        }
    }
    
    /**
     * 处理器状态
     */
    public static class ProcessorStatus {
        public final int corePoolSize;
        public final int maxPoolSize;
        public final int activeCount;
        public final int poolSize;
        public final int queueSize;
        public final long completedTaskCount;
        public final double currentRate;
        
        public ProcessorStatus(int corePoolSize, int maxPoolSize, int activeCount, 
                             int poolSize, int queueSize, long completedTaskCount, double currentRate) {
            this.corePoolSize = corePoolSize;
            this.maxPoolSize = maxPoolSize;
            this.activeCount = activeCount;
            this.poolSize = poolSize;
            this.queueSize = queueSize;
            this.completedTaskCount = completedTaskCount;
            this.currentRate = currentRate;
        }
        
        @Override
        public String toString() {
            return String.format("ProcessorStatus{核心线程=%d, 最大线程=%d, 活跃线程=%d, " +
                               "当前线程=%d, 队列大小=%d, 完成任务=%d, 当前速率=%.1f/秒}", 
                               corePoolSize, maxPoolSize, activeCount, poolSize, 
                               queueSize, completedTaskCount, currentRate);
        }
    }
}
