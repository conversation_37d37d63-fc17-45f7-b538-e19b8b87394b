package com.qudian.idle.bifrost.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 顺丰轨迹处理配置
 * 
 * <AUTHOR>
 * @since 2025/09/02
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sf.trace")
public class SfTraceConfig {
    
    /**
     * 是否启用并发处理
     */
    private boolean concurrentEnabled = true;
    
    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();
    
    /**
     * 流控配置
     */
    private RateLimit rateLimit = new RateLimit();
    
    /**
     * 批处理配置
     */
    private Batch batch = new Batch();
    
    /**
     * 弹性配置
     */
    private Resilience resilience = new Resilience();
    
    @Data
    public static class ThreadPool {
        private int coreSize = 10;
        private int maxSize = 20;
        private long keepAliveSeconds = 60;
        private int queueCapacity = 1000;
        private int taskTimeoutSeconds = 30;
    }
    
    @Data
    public static class RateLimit {
        private double maxCallsPerSecond = 30.0;
        private int acquireTimeoutSeconds = 10;
        private boolean warmupEnabled = true;
        private int warmupSeconds = 2;
    }
    
    @Data
    public static class Batch {
        private int size = 10;
        private int pageSize = 100;
    }
    
    @Data
    public static class Resilience {
        private CircuitBreaker circuitBreaker = new CircuitBreaker();
        private Retry retry = new Retry();
        
        @Data
        public static class CircuitBreaker {
            private float failureRateThreshold = 50.0f;
            private int waitDurationSeconds = 30;
            private int slidingWindowSize = 20;
            private int minimumNumberOfCalls = 10;
        }
        
        @Data
        public static class Retry {
            private int maxAttempts = 3;
            private int waitDurationSeconds = 1;
            private int exponentialBackoffMultiplier = 2;
        }
    }
}
