package com.qudian.idle.bifrost.server.config;

import com.ctrip.framework.apollo.core.ConfigConsts;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.qudian.idle.bifrost.common.config.AusTransportApiConfig;
import com.qudian.idle.bifrost.common.config.RateLimitConfig;
import com.qudian.idle.bifrost.common.config.ToolRemoteApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class ApolloRefreshConfig {
    private final AusTransportApiConfig ausTransportApiConfig;

    private final ToolRemoteApiConfig toolRemoteApiConfig;
    private final RateLimitConfig rateLimitConfig;
    private final RefreshScope refreshScope;

    public ApolloRefreshConfig(
            final AusTransportApiConfig ausTransportApiConfig,
            final RefreshScope refreshScope,
            final ToolRemoteApiConfig toolRemoteApiConfig,
            final RateLimitConfig rateLimitConfig) {
        this.ausTransportApiConfig = ausTransportApiConfig;
        this.toolRemoteApiConfig = toolRemoteApiConfig;
        this.rateLimitConfig = rateLimitConfig;
        this.refreshScope = refreshScope;
    }

    /**
     * 默认监听application命名空间
     * @param changeEvent
     */
    @ApolloConfigChangeListener()
    public void onChange(ConfigChangeEvent changeEvent) {
        log.info("ausTransportApiConfig before refresh {}", ausTransportApiConfig.toString());
        log.info("toolRemoteApiConfig before refresh {}", toolRemoteApiConfig.toString());
        log.info("rateLimitConfig before refresh {}", rateLimitConfig.toString());
        refreshScope.refresh("ausTransportApiConfig");
        refreshScope.refresh("toolRemoteApiConfig");
        refreshScope.refresh("rateLimitConfig");
        log.info("ausTransportApiConfig after refresh {}", ausTransportApiConfig);
        log.info("toolRemoteApiConfig after refresh {}", toolRemoteApiConfig);
        log.info("rateLimitConfig after refresh {}", rateLimitConfig);
    }
}
