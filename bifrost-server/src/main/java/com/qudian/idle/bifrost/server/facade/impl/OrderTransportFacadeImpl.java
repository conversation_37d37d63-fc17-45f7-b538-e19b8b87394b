package com.qudian.idle.bifrost.server.facade.impl;

import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.facade.OrderTransportFacade;
import com.qudian.idle.bifrost.api.vo.PagingList;
import com.qudian.idle.bifrost.api.vo.request.*;
import com.qudian.idle.bifrost.api.vo.response.*;
import com.qudian.idle.bifrost.api.vo.response.transport.TransportMessageVO;
import com.qudian.idle.bifrost.application.service.OrderTransportService;
import com.qudian.idle.bifrost.infrastructure.annotation.DubboService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Slf4j
public class OrderTransportFacadeImpl implements OrderTransportFacade {

    @Resource
    private OrderTransportService orderTransportService;
    

    @Override
    public BaseResponseDTO<String> cancelTransport(CancelTransportReqVO reqVO) {
        return orderTransportService.cancelTransport(reqVO);
    }

    @Override
    public BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(PushSingleTransportReqVO reqVO) {
        return orderTransportService.pushSingleTransport(reqVO);
    }

    @Override
    public BaseResponseDTO<PrintLabelResponseVO> printSingleLabel(SingleOrderTransportReqVO reqVO) {
        return ResponseBuilder.buildSuccess(orderTransportService.printSingleLabel(reqVO));
    }

    @Override
    public BaseResponseDTO<String> dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        return orderTransportService.dispatchSingleTransport(reqVO);
    }

    @Override
    public BaseResponseDTO<QuerySupportCarriersRespVO> querySupportCarriers(QuerySupportCarriersReqVO reqVO) {
        return ResponseBuilder.buildSuccess(orderTransportService.querySupportCarriers(reqVO));
    }

    @Override
    public BaseResponseDTO<QuerySupportCarriersRespVO> querySupportCarriersByCountry(SupportCarriersByCountryVO reqVO) {
        return ResponseBuilder.buildSuccess(orderTransportService.querySupportCarriersByCountry(reqVO));
    }

    @Override
    public BaseResponseDTO<PagingList<DeliverySettleListResVO>> queryChannelSettleRecordList(DeliverySettleListReqVO reqVO) {
        return ResponseBuilder.buildSuccess(orderTransportService.querySettleList(reqVO));
    }
}
