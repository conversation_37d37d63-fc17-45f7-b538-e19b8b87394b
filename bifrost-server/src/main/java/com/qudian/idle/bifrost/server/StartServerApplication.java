package com.qudian.idle.bifrost.server;

import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <p>文件名称:com.qudian.lme.driver.server.StartServerApplication</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/18
 */
@SpringBootApplication(scanBasePackages = {"com.qudian.**.*"})
@EnableCaching
@EnableAsync(proxyTargetClass = true)
@EnableScheduling
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@Slf4j
public class StartServerApplication {
    public static void main(String[] args) throws UnknownHostException {
        Long begin = System.currentTimeMillis();
        ConfigurableApplicationContext application = SpringApplication.run(StartServerApplication.class, args);
        Long end = System.currentTimeMillis();
        Environment env = application.getEnvironment();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("==>启动成功，启动时间：" + (end - begin) + "ms;");
        System.out.println("==>Local：" + "http://localhost:" + port + path+"/doc.html");
        log.info("Sentry initialized: {}", Sentry.isInitialized());
    }
}
