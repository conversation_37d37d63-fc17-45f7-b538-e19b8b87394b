package com.qudian.idle.bifrost.server.facade.impl;

import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.facade.ValleyEchosFacade;
import com.qudian.idle.bifrost.api.vo.request.ValleyEchosReqVO;
import com.qudian.idle.bifrost.infrastructure.annotation.DubboService;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.facade.impl.ValleyEchosFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/7/25
 */
@DubboService
@Slf4j
public class ValleyEchosFacadeImpl implements ValleyEchosFacade {
    @Override
    public BaseResponseDTO<ValleyEchosReqVO> echo(ValleyEchosReqVO reqVO) {
        log.info("Valley echos hey roar: {}", reqVO.getHeyRoar());
        ValleyEchosReqVO res = new ValleyEchosReqVO();
        res.setHeyRoar("[Server_echo] hi:" + reqVO.getHeyRoar());
        res.setOrigin(reqVO.getOrigin());
        return ResponseBuilder.buildSuccess(res);
    }
}
