package com.qudian.idle.bifrost.server.facade.impl.backdoor;

import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.facade.backdoor.BackdoorFacade;
import com.qudian.idle.bifrost.api.vo.request.backdoor.BackdoorOrderDeliveredReqVO;
import com.qudian.idle.bifrost.application.service.BackdoorService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: yangxinye
 * @Date: 2023/11/27
 * @Version: 1.0.0
 **/
@Service(version = "1.0.0")
@RestController
public class BackdoorFacadeImpl implements BackdoorFacade {

    @Resource
    private BackdoorService backdoorService;


    @Override
    @PostMapping("/backdoor/orderDelivered")
    public BaseResponseDTO<Boolean> orderDelivered(@RequestBody BackdoorOrderDeliveredReqVO reqVO) {
        return ResponseBuilder.buildSuccess(backdoorService.orderDelivered(reqVO));
    }
}
