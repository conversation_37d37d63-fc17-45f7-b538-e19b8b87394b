package com.qudian.idle.bifrost.server.job.trace.concurrent;

import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

/**
 * 基于Resilience4j的弹性API调用处理器
 * 提供熔断、重试等弹性机制
 * 
 * <AUTHOR>
 * @since 2025/09/02
 */
@Slf4j
@Component
public class SfTraceResilientProcessor {
    
    @Resource
    private ExpressService expressService;
    
    private CircuitBreaker circuitBreaker;
    private Retry retry;
    
    @PostConstruct
    public void init() {
        initCircuitBreaker();
        initRetry();
        registerEventListeners();
        log.info("弹性API调用处理器初始化完成");
    }
    
    /**
     * 初始化熔断器
     */
    private void initCircuitBreaker() {
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
            .failureRateThreshold(50)                    // 失败率阈值50%
            .waitDurationInOpenState(Duration.ofSeconds(30))  // 熔断器打开30秒
            .slidingWindowSize(20)                       // 滑动窗口20个请求
            .minimumNumberOfCalls(10)                    // 最少10个请求才计算失败率
            .slowCallRateThreshold(80)                   // 慢调用率阈值80%
            .slowCallDurationThreshold(Duration.ofSeconds(3))  // 慢调用时间阈值3秒
            .permittedNumberOfCallsInHalfOpenState(5)    // 半开状态允许5个请求
            .automaticTransitionFromOpenToHalfOpenEnabled(true)  // 自动转换到半开状态
            .build();
        
        this.circuitBreaker = CircuitBreaker.of("sfApiCircuitBreaker", config);
    }
    
    /**
     * 初始化重试器
     */
    private void initRetry() {
        RetryConfig config = RetryConfig.custom()
            .maxAttempts(3)                              // 最大重试3次
            .waitDuration(Duration.ofSeconds(1))         // 重试间隔1秒
            .retryExceptions(RuntimeException.class)     // 重试的异常类型
            .ignoreExceptions(IllegalArgumentException.class)  // 忽略的异常类型
            .build();
        
        this.retry = Retry.of("sfApiRetry", config);
    }
    
    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        // 熔断器事件监听
        circuitBreaker.getEventPublisher()
            .onStateTransition(event -> 
                log.info("熔断器状态变更: {} -> {}", 
                        event.getStateTransition().getFromState(), 
                        event.getStateTransition().getToState()))
            .onCallNotPermitted(event -> 
                log.warn("熔断器阻止调用: {}", event.getEventType()))
            .onError(event -> 
                log.error("熔断器记录错误: {}", event.getThrowable().getMessage()));
        
        // 重试事件监听
        retry.getEventPublisher()
            .onRetry(event -> 
                log.warn("API调用重试，第{}次重试，异常: {}", 
                        event.getNumberOfRetryAttempts(), 
                        event.getLastThrowable().getMessage()))
            .onSuccess(event -> 
                log.debug("API调用重试成功，重试次数: {}", event.getNumberOfRetryAttempts()));
    }
    
    /**
     * 弹性API调用
     * 集成熔断器和重试机制
     * 
     * @param waybillNos 运单号列表
     * @return API响应
     */
    public SearchRoutesRespDTO callSfApiResilient(List<String> waybillNos) {
        // 构建弹性调用链：熔断器 -> 重试器 -> 实际API调用
        Supplier<SearchRoutesRespDTO> decoratedSupplier = CircuitBreaker
            .decorateSupplier(circuitBreaker, () -> {
                return Retry.decorateSupplier(retry, () -> {
                    log.debug("调用顺丰API，运单数量: {}", waybillNos.size());
                    return expressService.searchRoutes(new SearchRoutesReqVO().setWaybillNo(waybillNos));
                }).get();
            });
        
        try {
            SearchRoutesRespDTO response = decoratedSupplier.get();
            log.debug("顺丰API调用成功，运单数量: {}", waybillNos.size());
            return response;
        } catch (Exception e) {
            log.error("弹性API调用最终失败，运单数量: {}", waybillNos.size(), e);
            throw e;
        }
    }
    
    /**
     * 检查熔断器状态
     * 
     * @return true 如果可以调用API
     */
    public boolean isApiCallable() {
        return circuitBreaker.getState() != CircuitBreaker.State.OPEN;
    }
    
    /**
     * 手动重置熔断器
     */
    public void resetCircuitBreaker() {
        circuitBreaker.reset();
        log.info("手动重置熔断器");
    }
    
    /**
     * 获取弹性处理器状态
     */
    public ResilientStatus getStatus() {
        return new ResilientStatus(
            circuitBreaker.getState().toString(),
            circuitBreaker.getMetrics().getFailureRate(),
            circuitBreaker.getMetrics().getSlowCallRate(),
            circuitBreaker.getMetrics().getNumberOfSuccessfulCalls(),
            circuitBreaker.getMetrics().getNumberOfFailedCalls(),
            retry.getMetrics().getNumberOfSuccessfulCallsWithRetryAttempt(),
            retry.getMetrics().getNumberOfFailedCallsWithRetryAttempt()
        );
    }
    
    /**
     * 弹性处理器状态
     */
    public static class ResilientStatus {
        public final String circuitBreakerState;
        public final float failureRate;
        public final float slowCallRate;
        public final long successfulCalls;
        public final long failedCalls;
        public final long successfulRetries;
        public final long failedRetries;
        
        public ResilientStatus(String circuitBreakerState, float failureRate, float slowCallRate,
                             long successfulCalls, long failedCalls, 
                             long successfulRetries, long failedRetries) {
            this.circuitBreakerState = circuitBreakerState;
            this.failureRate = failureRate;
            this.slowCallRate = slowCallRate;
            this.successfulCalls = successfulCalls;
            this.failedCalls = failedCalls;
            this.successfulRetries = successfulRetries;
            this.failedRetries = failedRetries;
        }
        
        @Override
        public String toString() {
            return String.format("ResilientStatus{熔断器状态=%s, 失败率=%.1f%%, 慢调用率=%.1f%%, " +
                               "成功调用=%d, 失败调用=%d, 成功重试=%d, 失败重试=%d}", 
                               circuitBreakerState, failureRate, slowCallRate, 
                               successfulCalls, failedCalls, successfulRetries, failedRetries);
        }
    }
}
