package com.qudian.idle.bifrost.server.facade.impl.express;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.api.facade.express.ExpressFacade;
import com.qudian.idle.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CancelExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.SearchExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.UpdateExpressReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchSingleRouteReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CancelExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.SearchExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.UpdateExpressRespVO;
import com.qudian.idle.bifrost.api.vo.response.express.trace.SearchSingleRouteRespVO;
import com.qudian.idle.bifrost.api.vo.share.RawDataList;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.server.job.trace.SfExpressTraceQueryJob;
import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@RestController
@Slf4j
@Service(version = "1.0.0")
@Api(tags = "ExpressFacadeImpl")
@Validated
public class ExpressFacadeImpl implements ExpressFacade {

    @Resource
    private ExpressService expressService;

    @Override
    @PostMapping("/tms/express/create")
    public BaseResponseDTO<CreateExpressRespVO> create(@RequestBody CreateExpressReqVO createExpressReqVO) {
        log.info("op=start_ExpressFacadeImpl.create, createExpressReqVO={}", createExpressReqVO);
        return ResponseBuilder.buildSuccess(expressService.create(createExpressReqVO));
    }

    @Override
    @PostMapping("/tms/express/update")
    public BaseResponseDTO<UpdateExpressRespVO> update(@RequestBody UpdateExpressReqVO updateExpressReqVO) {
        return ResponseBuilder.buildSuccess(expressService.update(updateExpressReqVO));
    }

    @Override
    @PostMapping("/tms/express/cancel")
    public BaseResponseDTO<CancelExpressRespVO> cancel(@RequestBody CancelExpressReqVO cancelExpressReqVO) {
        return ResponseBuilder.buildSuccess(expressService.cancel(cancelExpressReqVO));
    }

    @Override
    @PostMapping("/tms/express/batchPrint")
    public BaseResponseDTO<BatchPrintRespVO> batchPrint(@RequestBody BatchPrintReqVO batchPrintReqVO) {
        log.info("op=start_ExpressFacadeImpl.create, createExpressReqVO={}", batchPrintReqVO);
        return ResponseBuilder.buildSuccess(expressService.batchPrint(batchPrintReqVO));
    }

    @Resource
    private SfExpressTraceQueryJob traceQueryJob;

    @Override
    @PostMapping("/tms/express/searchRoutes")
    public BaseResponseDTO<String> searchRoutes(@RequestBody SearchRoutesReqVO searchRoutesReqVO) {
        log.info("op=start_ExpressFacadeImpl.create, createExpressReqVO={}", searchRoutesReqVO);
        traceQueryJob.doTask(1, DateUtil.parse("2015-08-28 00:00:00"));
        return ResponseBuilder.buildSuccess(JSON.toJSONString(expressService.searchRoutes(searchRoutesReqVO)));
    }

    @Override
    @PostMapping("/tms/express/searchExpress")
    public BaseResponseDTO<RawDataList<SearchExpressRespVO>> searchExpress(@RequestBody SearchExpressReqVO searchExpressReqVO) {
        return ResponseBuilder.buildSuccess(RawDataList.getRawDataList(expressService.searchExpress(searchExpressReqVO)));
    }

    @Override
    public BaseResponseDTO<SearchSingleRouteRespVO> querySingleRoute(SearchSingleRouteReqVO searchSingleRouteReqVO) {
        return ResponseBuilder.buildSuccess(
                new SearchSingleRouteRespVO().setWaybillRoute(expressService.querySingleRoute(searchSingleRouteReqVO))
        );
    }
}
