package com.qudian.idle.bifrost.server.job.trace;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderRepository;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import com.qudian.idle.bifrost.server.job.trace.concurrent.SfTraceConcurrentProcessor;
import com.qudian.idle.bifrost.server.config.SfTraceConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.job.trace.SfExpressTraceQueryJob</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "sf.trace", name = "concurrent-enabled", havingValue = "true", matchIfMissing = true)
public class SfExpressTraceQueryJob {
    @Resource
    private ExpressOrderRepository expressOrderRepository;
    @Resource
    private ExpressService expressService;
    @Resource
    private SfExpressTracePipeline tracePipeline;
    @Resource
    private SfTraceConcurrentProcessor concurrentProcessor;
    @Resource
    private SfTraceConfig config;

    private static final int PAGE_SIZE = 100; // 增大页面大小以配合并发处理

    /**
     * 并发处理模式（推荐）
     */
    @XxlJob("SfExpressTraceQueryJob")
    @Timed(value = "sf_trace_concurrent_job", description = "顺丰轨迹并发查询Job执行时间")
    public ReturnT<String> process(String param) {
        log.info("开始执行顺丰轨迹并发查询Job，参数: {}", param);

        try {
            // 解析参数
            int days = parseParam(param, 7);
            Date startTime = DateUtil.offsetDay(new Date(), -days);

            // 执行并发处理
            ProcessingSummary summary = doTaskConcurrently(1, startTime);

            log.info("并发处理完成: {}", summary);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("并发处理异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 串行处理模式（备用）
     */
    @XxlJob("SfExpressTraceQueryJobSerial")
    @Timed(value = "sf_trace_serial_job", description = "顺丰轨迹串行查询Job执行时间")
    public ReturnT<String> processSerial(String param) {
        log.info("开始执行顺丰轨迹串行查询Job，参数: {}", param);

        try {
            int days = parseParam(param, 7);
            Date startTime = DateUtil.offsetDay(new Date(), -days);
            this.doTask(1, startTime);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("串行处理异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 优雅的并发处理方法
     */
    public ProcessingSummary doTaskConcurrently(int pageNum, Date startTime) {
        int totalProcessed = 0;
        int totalSuccess = 0;
        int totalFailed = 0;
        long startTimeMs = System.currentTimeMillis();

        while (true) {
            Page<ExpressOrderPO> expressOrders = expressOrderRepository.page4TraceQuery(pageNum++, PAGE_SIZE, startTime);
            if (CollectionUtil.isEmpty(expressOrders.getRecords())) {
                log.info("没有更多数据需要处理，退出循环");
                break;
            }

            log.info("处理第{}页数据，订单数量: {}", pageNum - 1, expressOrders.getRecords().size());

            // 使用并发处理器处理当前页的数据
            SfTraceConcurrentProcessor.ProcessResult result =
                concurrentProcessor.processConcurrently(expressOrders.getRecords());

            totalProcessed += result.totalProcessed;
            totalSuccess += result.totalSuccess;
            totalFailed += result.totalFailed;

            log.info("第{}页处理完成，结果: {}", pageNum - 1, result);

            // 打印处理器状态
            SfTraceConcurrentProcessor.ProcessorStatus status = concurrentProcessor.getStatus();
            log.debug("处理器状态: {}", status);

            if (!expressOrders.hasNext()) {
                break;
            }
        }

        long endTimeMs = System.currentTimeMillis();
        ProcessingSummary summary = new ProcessingSummary(totalProcessed, totalSuccess, totalFailed,
                                                         endTimeMs - startTimeMs, pageNum - 1);
        log.info("并发处理任务完成: {}", summary);
        return summary;
    }

    /**
     * 传统串行处理方法（保留作为备用）
     */
    public void doTask(int pageNum, Date startTime) {
        log.info("开始串行处理顺丰轨迹查询任务");

        while (true) {
            Page<ExpressOrderPO> expressOrders = expressOrderRepository.page4TraceQuery(pageNum++, PAGE_SIZE, startTime);
            if (CollectionUtil.isEmpty(expressOrders.getRecords())) {
                break;
            }
            List<String> waybillNoS = expressOrders.getRecords().stream().map(ExpressOrderPO::getWaybillNo).collect(Collectors.toList());
            //调用顺丰查询路由接口
            SearchRoutesRespDTO respDTO = expressService.searchRoutes(new SearchRoutesReqVO().setWaybillNo(waybillNoS));
            tracePipeline.flowByQuery(respDTO);
            if (!expressOrders.hasNext()) {
                break;
            }
        }

        log.info("串行处理任务完成");
    }

    /**
     * 解析Job参数
     */
    private int parseParam(String param, int defaultDays) {
        if (param == null || param.trim().isEmpty()) {
            return defaultDays;
        }
        try {
            return Integer.parseInt(param.trim());
        } catch (NumberFormatException e) {
            log.warn("解析Job参数失败，使用默认值{}天，原始参数: {}", defaultDays, param);
            return defaultDays;
        }
    }

    /**
     * 处理总结
     */
    public static class ProcessingSummary {
        public final int totalProcessed;
        public final int totalSuccess;
        public final int totalFailed;
        public final long processingTimeMs;
        public final int totalPages;
        public final double successRate;
        public final double throughput;

        public ProcessingSummary(int totalProcessed, int totalSuccess, int totalFailed,
                               long processingTimeMs, int totalPages) {
            this.totalProcessed = totalProcessed;
            this.totalSuccess = totalSuccess;
            this.totalFailed = totalFailed;
            this.processingTimeMs = processingTimeMs;
            this.totalPages = totalPages;
            this.successRate = totalProcessed > 0 ? (totalSuccess * 100.0 / totalProcessed) : 0;
            this.throughput = processingTimeMs > 0 ? (totalProcessed * 1000.0 / processingTimeMs) : 0;
        }

        @Override
        public String toString() {
            return String.format("ProcessingSummary{总处理=%d, 成功=%d, 失败=%d, " +
                               "成功率=%.1f%%, 耗时=%dms, 吞吐量=%.1f个/秒, 总页数=%d}",
                               totalProcessed, totalSuccess, totalFailed,
                               successRate, processingTimeMs, throughput, totalPages);
        }
    }
}
