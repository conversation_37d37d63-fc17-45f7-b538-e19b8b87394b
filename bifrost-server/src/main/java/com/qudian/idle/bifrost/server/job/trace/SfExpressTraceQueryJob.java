package com.qudian.idle.bifrost.server.job.trace;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchRoutesReqVO;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.idle.bifrost.application.service.ExpressService;
import com.qudian.idle.bifrost.infrastructure.repository.ExpressOrderRepository;
import com.qudian.idle.bifrost.infrastructure.repository.database.po.ExpressOrderPO;
import com.qudian.idle.bifrost.infrastructure.repository.remote.dto.sf.trace.SearchRoutesRespDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.job.trace.SfExpressTraceQueryJob</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/30
 */
@Component
@Slf4j
public class SfExpressTraceQueryJob {
    @Resource
    private ExpressOrderRepository expressOrderRepository;
    @Resource
    private ExpressService expressService;
    @Resource
    private SfExpressTracePipeline tracePipeline;

    private static final int PAGE_SIZE = 10;

    @XxlJob("SfExpressTraceQueryJob")
    public ReturnT<String> process(String param) {
        //TODO@chr.通过param定制查询参数
        //查询待查询的运单,更新时间7天在内的
        Date startTime = DateUtil.offsetDay(new Date(), -7);
        int pageNum = 1;
        this.doTask(pageNum, startTime);
        return ReturnT.SUCCESS;
    }

    public void doTask(int pageNum, Date startTime) {
        while (true) {
            Page<ExpressOrderPO> expressOrders = expressOrderRepository.page4TraceQuery(pageNum++, PAGE_SIZE, startTime);
            if (CollectionUtil.isEmpty(expressOrders.getRecords())) {
                break;
            }
            List<String> waybillNoS = expressOrders.getRecords().stream().map(ExpressOrderPO::getWaybillNo).collect(Collectors.toList());
            //调用顺丰查询路由接口
            SearchRoutesRespDTO respDTO = expressService.searchRoutes(new SearchRoutesReqVO().setWaybillNo(waybillNoS));
            tracePipeline.flowByQuery(respDTO);
            if (!expressOrders.hasNext()) {
                break;
            }
        }
    }
}
