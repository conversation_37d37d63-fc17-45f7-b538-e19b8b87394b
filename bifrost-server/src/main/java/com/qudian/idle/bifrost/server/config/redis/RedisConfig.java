package com.qudian.idle.bifrost.server.config.redis;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @desc: redis配置
 * @author: <EMAIL>
 * @date: Created at 2020-11-06 10:01
 */
@Configuration
@EnableCaching
@EnableConfigurationProperties({ExpireCache.class})
@Slf4j
public class RedisConfig extends CachingConfigurerSupport {

    private Long cacheRedisTimeToLive = 600L;
    @Autowired
    private ExpireCache expireCache;

    public RedisConfig() {
    }

    @Bean({"redisTemplate"})
    @ConditionalOnMissingBean(
            name = {"redisTemplate"}
    )
    public RedisTemplate redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisSerializer fastJson2JsonRedisSerializer = new FastJson2JsonRedisSerializer(Object.class);
        RedisTemplate redisTemplate = new RedisTemplate();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(fastJson2JsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(fastJson2JsonRedisSerializer);
        redisTemplate.setDefaultSerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean({"cacheManager"})
    @ConditionalOnMissingBean(
            name = {"cacheManager"}
    )
    public CacheManager redisCacheManager(RedisConnectionFactory connectionFactory) {
        final RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(this.cacheRedisTimeToLive)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new FastJson2JsonRedisSerializer(Object.class))).disableCachingNullValues();
        final Set<String> expiredCacheKeySet = new HashSet();
        final ConcurrentHashMap<String, RedisCacheConfiguration> expireCacheConfig = new ConcurrentHashMap();
        Optional.of(this.expireCache.getInitCaches()).ifPresent(stringDurationMap -> {
            Iterator var2 = stringDurationMap.entrySet().iterator();

            while (var2.hasNext()) {
                Map.Entry<String, Duration> entry = (Map.Entry) var2.next();
                expiredCacheKeySet.add(entry.getKey());
                expireCacheConfig.put(entry.getKey(), config.entryTtl((Duration) entry.getValue()));
            }

        });
        return RedisCacheManager.builder(connectionFactory).cacheDefaults(config).initialCacheNames(expiredCacheKeySet).withInitialCacheConfigurations(expireCacheConfig).transactionAware().build();
    }

    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        CacheErrorHandler cacheErrorHandler = new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
                log.error("try to get {} from {} throw exception, continue to get from db", new Object[]{key, cache.getName(), exception});
            }

            @Override
            public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
                log.error("try to put key:{}, value:{} to {} throw exception, continue to add into db", new Object[]{key, JSON.toJSON(value), cache.getName(), exception});
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
                log.error("try to delete {} from {} throw exception, continue to delete from db", new Object[]{key, cache.getName(), exception});
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, Cache cache) {
                log.error("try to clear set {} throw exception, continue to clear db", cache.getName(), exception);
            }
        };
        return cacheErrorHandler;
    }
}
