package com.qudian.idle.bifrost.server.facade.impl.express;

import com.qudian.idle.bifrost.api.facade.express.ExpressCallbackFacade;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SfExpressTraceCallbackReqVO;
import com.qudian.idle.bifrost.api.vo.response.express.TraceCallbackRespVO;
import com.qudian.idle.bifrost.application.convertor.trace.SfExpressRouteStruct;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.idle.bifrost.infrastructure.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.facade.impl.express.ExpressCallbackFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@DubboService
public class ExpressCallbackFacadeImpl implements ExpressCallbackFacade {
    @Resource
    private SfExpressTracePipeline sfExpressTracePipeline;
    @Resource
    private SfExpressRouteStruct routeStruct;

    @Override
    public TraceCallbackRespVO traceCallback(SfExpressTraceCallbackReqVO reqVO) {
        sfExpressTracePipeline.flowByCallback(routeStruct.voToDto(reqVO));
        return TraceCallbackRespVO.success();
    }
}
