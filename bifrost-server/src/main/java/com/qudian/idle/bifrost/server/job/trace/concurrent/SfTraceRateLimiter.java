package com.qudian.idle.bifrost.server.job.trace.concurrent;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 基于Google Guava的顺丰API流控器
 * 使用令牌桶算法确保每秒最多30次调用
 * 
 * <AUTHOR>
 * @since 2025/09/02
 */
@Slf4j
@Component
public class SfTraceRateLimiter {
    
    /**
     * 每秒最大调用次数（顺丰API限制）
     */
    private static final double MAX_PERMITS_PER_SECOND = 30.0;
    
    /**
     * Guava RateLimiter实例
     */
    private RateLimiter rateLimiter;
    
    @PostConstruct
    public void init() {
        // 创建令牌桶，每秒30个令牌，支持突发流量
        this.rateLimiter = RateLimiter.create(MAX_PERMITS_PER_SECOND);
        log.info("初始化顺丰API流控器，每秒最大调用次数: {}", MAX_PERMITS_PER_SECOND);
    }
    
    /**
     * 尝试获取调用许可（非阻塞）
     * 
     * @return true 如果获取成功，false 如果当前无可用令牌
     */
    public boolean tryAcquire() {
        boolean acquired = rateLimiter.tryAcquire();
        if (!acquired) {
            //log.debug("当前无可用API调用令牌");
        }
        return acquired;
    }
    
    /**
     * 尝试在指定时间内获取调用许可
     * 
     * @param timeout 超时时间
     * @return true 如果在超时时间内获取成功
     */
    public boolean tryAcquire(Duration timeout) {
        boolean acquired = rateLimiter.tryAcquire(timeout.toMillis(), TimeUnit.MILLISECONDS);
        if (!acquired) {
            log.debug("在{}ms内未能获取到API调用令牌", timeout.toMillis());
        }
        return acquired;
    }
    
    /**
     * 阻塞获取调用许可
     * 会等待直到有可用令牌
     */
    public void acquire() {
        double waitTime = rateLimiter.acquire();
        if (waitTime > 0) {
            log.debug("获取API调用令牌，等待时间: {}秒", String.format("%.3f", waitTime));
        }
    }
    
    /**
     * 批量获取多个许可
     * 
     * @param permits 需要获取的许可数量
     * @return 实际等待的时间（秒）
     */
    public double acquire(int permits) {
        double waitTime = rateLimiter.acquire(permits);
        log.debug("批量获取{}个API调用令牌，等待时间: {}秒", permits, String.format("%.3f", waitTime));
        return waitTime;
    }
    
    /**
     * 动态调整速率
     * 
     * @param permitsPerSecond 新的每秒许可数
     */
    public void setRate(double permitsPerSecond) {
        if (permitsPerSecond > 0 && permitsPerSecond <= 100) {
            rateLimiter.setRate(permitsPerSecond);
            log.info("动态调整API调用速率为每秒{}次", permitsPerSecond);
        } else {
            log.warn("无效的速率设置: {}，保持当前速率", permitsPerSecond);
        }
    }
    
    /**
     * 获取当前配置的速率
     * 
     * @return 每秒许可数
     */
    public double getRate() {
        return rateLimiter.getRate();
    }
    
    /**
     * 预热流控器
     * 避免冷启动时的突发延迟
     * 
     * @param warmupDuration 预热时间
     */
    public void warmUp(Duration warmupDuration) {
        log.info("开始预热API流控器，预热时间: {}ms", warmupDuration.toMillis());
        long endTime = System.currentTimeMillis() + warmupDuration.toMillis();
        
        while (System.currentTimeMillis() < endTime) {
            rateLimiter.tryAcquire();
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.info("API流控器预热完成");
    }
}
