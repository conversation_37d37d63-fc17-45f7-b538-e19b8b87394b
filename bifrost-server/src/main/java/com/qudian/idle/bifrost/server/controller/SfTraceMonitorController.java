package com.qudian.idle.bifrost.server.controller;

import com.google.common.collect.ImmutableMap;
import com.qudian.idle.bifrost.server.job.trace.concurrent.SfTraceConcurrentProcessor;
import com.qudian.idle.bifrost.server.job.trace.concurrent.SfTraceRateLimiter;
import com.qudian.idle.bifrost.server.job.trace.concurrent.SfTraceResilientProcessor;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 顺丰轨迹处理监控控制器
 *
 * <AUTHOR>
 * @since 2025/09/02
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor/sf-trace")
public class SfTraceMonitorController {

    @Resource
    private SfTraceConcurrentProcessor concurrentProcessor;

    @Resource
    private SfTraceRateLimiter rateLimiter;

    @Resource
    private SfTraceResilientProcessor resilientProcessor;

    /**
     * 获取处理器状态
     */
    @GetMapping("/status")
    @Timed(value = "sf_trace_monitor", description = "监控接口调用时间")
    public Map<String, Object> getStatus() {
        Map<String, Object> result = new HashMap<>();

        // 并发处理器状态
        SfTraceConcurrentProcessor.ProcessorStatus processorStatus = concurrentProcessor.getStatus();
        Map<String, Object> processor = new HashMap<>();
        processor.put("corePoolSize", processorStatus.corePoolSize);
        processor.put("maxPoolSize", processorStatus.maxPoolSize);
        processor.put("activeCount", processorStatus.activeCount);
        processor.put("poolSize", processorStatus.poolSize);
        processor.put("queueSize", processorStatus.queueSize);
        processor.put("completedTaskCount", processorStatus.completedTaskCount);

        // 流控器状态
        Map<String, Object> rateLimit = new HashMap<>();
        rateLimit.put("currentRate", rateLimiter.getRate());
        rateLimit.put("maxRate", 30.0);

        // 弹性处理器状态
        SfTraceResilientProcessor.ResilientStatus resilientStatus = resilientProcessor.getStatus();
        Map<String, Object> resilient = new HashMap<>();
        resilient.put("circuitBreakerState", resilientStatus.circuitBreakerState);
        resilient.put("failureRate", resilientStatus.failureRate);
        resilient.put("slowCallRate", resilientStatus.slowCallRate);
        resilient.put("successfulCalls", resilientStatus.successfulCalls);
        resilient.put("failedCalls", resilientStatus.failedCalls);
        resilient.put("apiCallable", resilientProcessor.isApiCallable());

        result.put("processor", processor);
        result.put("rateLimit", rateLimit);
        result.put("resilient", resilient);
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    /**
     * 动态调整流控速率
     */
    @PostMapping("/rate-limit/adjust")
    @Timed(value = "sf_trace_adjust", description = "调整流控速率")
    public Map<String, Object> adjustRateLimit(@RequestParam double permitsPerSecond) {
        if (permitsPerSecond <= 0 || permitsPerSecond > 100) {
            throw new IllegalArgumentException("流控速率必须在0-100之间");
        }

        rateLimiter.setRate(permitsPerSecond);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "流控速率已调整为每秒" + permitsPerSecond + "次");
        result.put("newRate", permitsPerSecond);
        result.put("timestamp", System.currentTimeMillis());

        log.info("流控速率已动态调整为每秒{}次", permitsPerSecond);
        return result;
    }

    /**
     * 重置熔断器
     */
    @PostMapping("/circuit-breaker/reset")
    @Timed(value = "sf_trace_reset", description = "重置熔断器")
    public Map<String, Object> resetCircuitBreaker() {
        resilientProcessor.resetCircuitBreaker();

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "熔断器已重置");
        result.put("timestamp", System.currentTimeMillis());

        log.info("熔断器已手动重置");
        return result;
    }

    /**
     * 预热流控器
     */
    @PostMapping("/rate-limit/warmup")
    @Timed(value = "sf_trace_warmup", description = "预热流控器")
    public Map<String, Object> warmupRateLimiter(@RequestParam(defaultValue = "2") int seconds) {
        if (seconds <= 0 || seconds > 60) {
            throw new IllegalArgumentException("预热时间必须在1-60秒之间");
        }

        rateLimiter.warmUp(Duration.ofSeconds(seconds));

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "流控器预热完成，预热时间" + seconds + "秒");
        result.put("warmupSeconds", seconds);
        result.put("timestamp", System.currentTimeMillis());

        log.info("流控器预热完成，预热时间{}秒", seconds);
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();

        try {
            SfTraceConcurrentProcessor.ProcessorStatus processorStatus = concurrentProcessor.getStatus();
            boolean processorHealthy = processorStatus.activeCount < processorStatus.maxPoolSize * 0.9;
            boolean apiCallable = resilientProcessor.isApiCallable();

            boolean healthy = processorHealthy && apiCallable;

            result.put("status", healthy ? "UP" : "DOWN");
            result.put("details", ImmutableMap.of(
                    "processor", processorHealthy ? "UP" : "BUSY",
                    "circuitBreaker", apiCallable ? "CLOSED" : "OPEN",
                    "rateLimit", "UP"
            ));
            result.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("健康检查异常", e);
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }
}
