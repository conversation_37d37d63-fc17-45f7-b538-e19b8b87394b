package com.qudian.idle.bifrost.server.service.trace;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressOpCodeConfig;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.idle.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.idle.bifrost.common.exception.BizException;
import com.qudian.idle.bifrost.infrastructure.repository.mq.MqProducer;
import com.qudian.idle.bifrost.server.StartTestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.service.trace.SfExpressTracePipelineTest</p>
 * <p>文件描述: 顺丰物流轨迹-处理管道测试类</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Slf4j
public class SfExpressTracePipelineTest extends StartTestApplication {
    @Resource
    private SfExpressTracePipeline sfExpressTracePipeline;
    @Resource
    private SfExpressOpCodeConfig opCodeConfig;
    @MockBean
    private MqProducer mqProducer;

    /**
     * 测试正常的顺丰轨迹数据处理流程
     */
    @Test
    public void testFlow_ValidRouteData_ShouldProcessSuccessfully() {
        String validRouteJSON = buildValidRouteJSON();
        doAnswer(invocation -> null).when(mqProducer).sendEntityMsg(any(), anyString(), any());
        sfExpressTracePipeline.flowByJSON(validRouteJSON);
        // 验证MQ发送被调用（只有签收事件发送MQ）
        verify(mqProducer, times(1)).sendEntityMsg(any(), eq("delivered"), eq("SF7444400031887"));
    }

    /**
     * 测试空JSON输入
     */
    @Test
    public void testFlow_EmptyJSON_ShouldHandleGracefully() {
        assertThatThrownBy(() -> sfExpressTracePipeline.flowByJSON(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("顺丰轨迹数据[JSON]不能为空");
        verify(mqProducer, never()).sendEntityMsg(any(), anyString(), any());
    }

    /**
     * 测试无效JSON格式
     */
    @Test
    public void testFlow_InvalidJSON_ShouldHandleGracefully() {
        assertThatThrownBy(() -> sfExpressTracePipeline.flowByJSON("{invalid json"))
                .isInstanceOf(BizException.class)
                .hasMessageContaining("解析顺丰轨迹数据失败");
        verify(mqProducer, never()).sendEntityMsg(any(), anyString(), any());
    }

    /**
     * 测试空的WaybillRoute
     */
    @Test
    public void testFlow_EmptyWaybillRoute_ShouldHandleGracefully() {
        SfExpressRouteDTO emptyRoute = new SfExpressRouteDTO();
        emptyRoute.setWaybillRoute(Collections.emptyList());
        String emptyRouteJSON = JSON.toJSONString(emptyRoute);

        assertThatThrownBy(() -> sfExpressTracePipeline.flowByJSON(emptyRouteJSON))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("顺丰轨迹数据[waybillRoute]不能为空");
        verify(mqProducer, never()).sendEntityMsg(any(), anyString(), any());
    }

    /**
     * 测试只包含非签收事件的轨迹数据
     */
    @Test
    public void testFlow_OnlyNonDeliveredEvents_ShouldNotSendMQ() {
        String nonDeliveredRouteJSON = buildNonDeliveredRouteJSON();
        sfExpressTracePipeline.flowByJSON(nonDeliveredRouteJSON);
        // 验证MQ不会被调用（因为没有签收事件）
        verify(mqProducer, never()).sendEntityMsg(any(), anyString(), any());
    }

    /**
     * 测试包含未知opCode的轨迹数据
     */
    @Test
    public void testFlow_UnknownOpCode_ShouldSkipUnknownEvents() {
        String unknownOpCodeJSON = buildUnknownOpCodeRouteJSON();
        // Mock MQ发送成功（只有已知的签收事件会发送）
        doAnswer(invocation -> null).when(mqProducer).sendEntityMsg(any(), anyString(), any());
        sfExpressTracePipeline.flowByJSON(unknownOpCodeJSON);
        verify(mqProducer, times(1)).sendEntityMsg(any(), eq("delivered"), eq("SF7444400031887"));
    }

    /**
     * 测试MQ发送失败的情况
     */
    @Test
    public void testFlow_MQSendFailure_ShouldContinueProcessing() {
        String validRouteJSON = buildValidRouteJSON();
        doThrow(new RuntimeException("MQ发送失败")).when(mqProducer).sendEntityMsg(any(), anyString(), any());
        sfExpressTracePipeline.flowByJSON(validRouteJSON);
        // 验证MQ发送被尝试调用
        verify(mqProducer, times(1)).sendEntityMsg(any(), eq("delivered"), eq("SF7444400031887"));
    }

    /**
     * 测试多个签收事件的处理
     */
    @Test
    public void testFlow_MultipleDeliveredEvents_ShouldSendMultipleMQ() {
        String multipleDeliveredJSON = buildMultipleDeliveredRouteJSON();
        doAnswer(invocation -> null).when(mqProducer).sendEntityMsg(any(), anyString(), any());
        sfExpressTracePipeline.flowByJSON(multipleDeliveredJSON);
        // 验证发送了2条MQ
        verify(mqProducer, times(2)).sendEntityMsg(any(), eq("delivered"), anyString());
    }

    /**
     * 测试时间解析功能
     */
    @Test
    public void testParseEventTime_ValidTime_ShouldReturnCorrectTimestamp() {
        Object result = ReflectionTestUtils.invokeMethod(sfExpressTracePipeline, "parseEventTime", "2020-05-11 16:56:54");
        assertNotNull("解析结果不应为null", result);
        assertTrue("应该返回Long类型", result instanceof Long);

        Long timestamp = (Long) result;
        assertTrue("时间戳应该大于0", timestamp > 0);
    }

    /**
     * 测试无效时间格式的解析
     */
    @Test
    public void testParseEventTime_InvalidTime_ShouldReturnCurrentTime() {
        long beforeCall = System.currentTimeMillis();
        Object result = ReflectionTestUtils.invokeMethod(sfExpressTracePipeline, "parseEventTime", "invalid-time");
        long afterCall = System.currentTimeMillis();

        assertNotNull("解析结果不应为null", result);
        assertTrue("应该返回Long类型", result instanceof Long);
        Long timestamp = (Long) result;
        assertTrue("应该返回当前时间附近的时间戳", timestamp >= beforeCall && timestamp <= afterCall);
    }

    /**
     * 测试空时间的解析
     */
    @Test
    public void testParseEventTime_EmptyTime_ShouldReturnCurrentTime() {
        long beforeCall = System.currentTimeMillis();
        Object result = ReflectionTestUtils.invokeMethod(sfExpressTracePipeline, "parseEventTime", "");
        long afterCall = System.currentTimeMillis();

        assertNotNull("解析结果不应为null", result);
        assertTrue("应该返回Long类型", result instanceof Long);
        Long timestamp = (Long) result;
        assertTrue("应该返回当前时间附近的时间戳", timestamp >= beforeCall && timestamp <= afterCall);
    }

    /**
     * 测试完整的物流轨迹处理流程
     */
    @Test
    public void testFlow_CompleteRoute_ShouldProcessAllEvents() {
        String completeRouteJSON = buildCompleteRouteJSON();
        // 捕获MQ发送的参数
        ArgumentCaptor<Object> mqBodyCaptor = ArgumentCaptor.forClass(Object.class);
        ArgumentCaptor<String> tagCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Object> keyCaptor = ArgumentCaptor.forClass(Object.class);
        doAnswer(invocation -> null).when(mqProducer).sendEntityMsg(mqBodyCaptor.capture(), tagCaptor.capture(), keyCaptor.capture());

        sfExpressTracePipeline.flowByJSON(completeRouteJSON);
        assertEquals("应该发送delivered标签", "delivered", tagCaptor.getValue());
        assertEquals("应该使用运单号作为key", "SF7444400031892", keyCaptor.getValue());
        assertNotNull("MQ消息体不应为空", mqBodyCaptor.getValue());
    }

    /**
     * 测试OpCodeConfig的功能
     */
    @Test
    public void testOpCodeConfig_KnownOpCodes_ShouldReturnCorrectEventTypes() {
        // 测试揽收事件
        assertTrue("50应该是揽收事件", opCodeConfig.getEventType("50").isPresent());
        assertEquals("50应该是PICKUP类型",
            SfExpressOpCodeConfig.SfExpressEventType.PICKUP,
            opCodeConfig.getEventType("50").get());

        // 测试签收事件
        assertTrue("80应该是签收事件", opCodeConfig.getEventType("80").isPresent());
        assertEquals("80应该是DELIVERED类型",
            SfExpressOpCodeConfig.SfExpressEventType.DELIVERED,
            opCodeConfig.getEventType("80").get());

        // 测试运输中事件
        assertTrue("10应该是运输中事件", opCodeConfig.getEventType("10").isPresent());
        assertEquals("10应该是IN_TRANSIT类型",
            SfExpressOpCodeConfig.SfExpressEventType.IN_TRANSIT,
            opCodeConfig.getEventType("10").get());

        // 测试未知事件
        assertFalse("999应该是未知事件", opCodeConfig.getEventType("999").isPresent());
    }

    /**
     * 测试签收通知判断
     */
    @Test
    public void testOpCodeConfig_DeliveryNotification_ShouldReturnCorrectFlags() {
        // 签收事件需要发送通知
        assertTrue("80应该需要发送签收通知", opCodeConfig.needsDeliveryNotification("80"));
        assertTrue("128应该需要发送签收通知", opCodeConfig.needsDeliveryNotification("128"));

        // 非签收事件不需要发送通知
        assertFalse("50不应该发送签收通知", opCodeConfig.needsDeliveryNotification("50"));
        assertFalse("10不应该发送签收通知", opCodeConfig.needsDeliveryNotification("10"));

        // 未知事件不需要发送通知
        assertFalse("999不应该发送签收通知", opCodeConfig.needsDeliveryNotification("999"));
    }

    // ==================== 辅助方法 ====================

    /**
     * 构建有效的路由JSON数据（包含揽收和签收事件）
     */
    private String buildValidRouteJSON() {
        SfExpressRouteDTO routeDTO = new SfExpressRouteDTO();

        SfExpressRouteDTO.SfExpressRouteDetailDTO pickupEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        pickupEvent.setWaybillNo("SF7444400031887");
        pickupEvent.setAcceptAddress("深圳市");
        pickupEvent.setReasonName("");
        pickupEvent.setOrderId("202003225d33322239ddW1df5t3");
        pickupEvent.setAcceptTime("2020-05-11 16:56:54");
        pickupEvent.setRemark("顺丰速运 已收取快件");
        pickupEvent.setOpCode("50"); // 揽收事件
        pickupEvent.setId("158918741444476");
        pickupEvent.setReasonCode("");

        SfExpressRouteDTO.SfExpressRouteDetailDTO deliveredEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        deliveredEvent.setWaybillNo("SF7444400031887");
        deliveredEvent.setAcceptAddress("北京市");
        deliveredEvent.setReasonName("");
        deliveredEvent.setOrderId("202003225d33322239ddW1df5t3");
        deliveredEvent.setAcceptTime("2020-05-12 10:30:00");
        deliveredEvent.setRemark("快件已签收");
        deliveredEvent.setOpCode("80"); // 签收事件
        deliveredEvent.setId("158918741457126");
        deliveredEvent.setReasonCode("");

        routeDTO.setWaybillRoute(Arrays.asList(pickupEvent, deliveredEvent));
        return JSON.toJSONString(routeDTO);
    }

    /**
     * 构建只包含非签收事件的路由JSON数据
     */
    private String buildNonDeliveredRouteJSON() {
        SfExpressRouteDTO routeDTO = new SfExpressRouteDTO();

        SfExpressRouteDTO.SfExpressRouteDetailDTO pickupEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        pickupEvent.setWaybillNo("SF7444400031888");
        pickupEvent.setAcceptAddress("深圳市");
        pickupEvent.setOrderId("202003225d33322239ddW1df5t4");
        pickupEvent.setAcceptTime("2020-05-11 16:56:54");
        pickupEvent.setRemark("顺丰速运 已收取快件");
        pickupEvent.setOpCode("50"); // 揽收事件
        pickupEvent.setId("158918741444477");

        SfExpressRouteDTO.SfExpressRouteDetailDTO transitEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        transitEvent.setWaybillNo("SF7444400031888");
        transitEvent.setAcceptAddress("郑州市");
        transitEvent.setOrderId("202003225d33322239ddW1df5t4");
        transitEvent.setAcceptTime("2020-05-11 20:30:00");
        transitEvent.setRemark("快件到达 【郑州园博中转场】");
        transitEvent.setOpCode("10"); // 运输中事件
        transitEvent.setId("158918741457127");

        routeDTO.setWaybillRoute(Arrays.asList(pickupEvent, transitEvent));
        return JSON.toJSONString(routeDTO);
    }

    /**
     * 构建包含未知opCode的路由JSON数据
     */
    private String buildUnknownOpCodeRouteJSON() {
        SfExpressRouteDTO routeDTO = new SfExpressRouteDTO();

        SfExpressRouteDTO.SfExpressRouteDetailDTO unknownEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        unknownEvent.setWaybillNo("SF7444400031889");
        unknownEvent.setAcceptAddress("上海市");
        unknownEvent.setOrderId("202003225d33322239ddW1df5t5");
        unknownEvent.setAcceptTime("2020-05-11 16:56:54");
        unknownEvent.setRemark("未知事件");
        unknownEvent.setOpCode("999"); // 未知opCode
        unknownEvent.setId("158918741444478");

        SfExpressRouteDTO.SfExpressRouteDetailDTO deliveredEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        deliveredEvent.setWaybillNo("SF7444400031887");
        deliveredEvent.setAcceptAddress("北京市");
        deliveredEvent.setOrderId("202003225d33322239ddW1df5t3");
        deliveredEvent.setAcceptTime("2020-05-12 10:30:00");
        deliveredEvent.setRemark("快件已签收");
        deliveredEvent.setOpCode("80"); // 签收事件
        deliveredEvent.setId("158918741457128");

        routeDTO.setWaybillRoute(Arrays.asList(unknownEvent, deliveredEvent));
        return JSON.toJSONString(routeDTO);
    }

    /**
     * 构建包含多个签收事件的路由JSON数据
     */
    private String buildMultipleDeliveredRouteJSON() {
        SfExpressRouteDTO routeDTO = new SfExpressRouteDTO();

        SfExpressRouteDTO.SfExpressRouteDetailDTO deliveredEvent1 = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        deliveredEvent1.setWaybillNo("SF7444400031890");
        deliveredEvent1.setAcceptAddress("北京市");
        deliveredEvent1.setOrderId("202003225d33322239ddW1df5t6");
        deliveredEvent1.setAcceptTime("2020-05-12 10:30:00");
        deliveredEvent1.setRemark("快件已签收");
        deliveredEvent1.setOpCode("80"); // 签收事件
        deliveredEvent1.setId("158918741457129");

        SfExpressRouteDTO.SfExpressRouteDetailDTO deliveredEvent2 = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        deliveredEvent2.setWaybillNo("SF7444400031891");
        deliveredEvent2.setAcceptAddress("上海市");
        deliveredEvent2.setOrderId("202003225d33322239ddW1df5t7");
        deliveredEvent2.setAcceptTime("2020-05-12 14:20:00");
        deliveredEvent2.setRemark("客户从丰巢取件成功");
        deliveredEvent2.setOpCode("128"); // 丰巢签收事件
        deliveredEvent2.setId("158918741457130");

        routeDTO.setWaybillRoute(Arrays.asList(deliveredEvent1, deliveredEvent2));
        return JSON.toJSONString(routeDTO);
    }

    /**
     * 构建包含各种事件类型的完整测试数据
     */
    private String buildCompleteRouteJSON() {
        SfExpressRouteDTO routeDTO = new SfExpressRouteDTO();
        // 揽收事件
        SfExpressRouteDTO.SfExpressRouteDetailDTO pickupEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        pickupEvent.setWaybillNo("SF7444400031892");
        pickupEvent.setAcceptAddress("深圳市");
        pickupEvent.setOrderId("202003225d33322239ddW1df5t8");
        pickupEvent.setAcceptTime("2020-05-11 16:56:54");
        pickupEvent.setRemark("顺丰速运 已收取快件");
        pickupEvent.setOpCode("50");
        pickupEvent.setId("158918741444479");
        // 运输中事件
        SfExpressRouteDTO.SfExpressRouteDetailDTO transitEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        transitEvent.setWaybillNo("SF7444400031892");
        transitEvent.setAcceptAddress("郑州市");
        transitEvent.setOrderId("202003225d33322239ddW1df5t8");
        transitEvent.setAcceptTime("2020-05-11 20:30:00");
        transitEvent.setRemark("快件到达 【郑州园博中转场】");
        transitEvent.setOpCode("10");
        transitEvent.setId("158918741457131");
        // 派送中事件
        SfExpressRouteDTO.SfExpressRouteDetailDTO outForDeliveryEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        outForDeliveryEvent.setWaybillNo("SF7444400031892");
        outForDeliveryEvent.setAcceptAddress("北京市");
        outForDeliveryEvent.setOrderId("202003225d33322239ddW1df5t8");
        outForDeliveryEvent.setAcceptTime("2020-05-12 08:00:00");
        outForDeliveryEvent.setRemark("正在派送途中");
        outForDeliveryEvent.setOpCode("70");
        outForDeliveryEvent.setId("158918741457132");
        // 签收事件
        SfExpressRouteDTO.SfExpressRouteDetailDTO deliveredEvent = new SfExpressRouteDTO.SfExpressRouteDetailDTO();
        deliveredEvent.setWaybillNo("SF7444400031892");
        deliveredEvent.setAcceptAddress("北京市");
        deliveredEvent.setOrderId("202003225d33322239ddW1df5t8");
        deliveredEvent.setAcceptTime("2020-05-12 10:30:00");
        deliveredEvent.setRemark("快件已签收");
        deliveredEvent.setOpCode("80");
        deliveredEvent.setId("158918741457133");
        routeDTO.setWaybillRoute(Arrays.asList(pickupEvent, transitEvent, outForDeliveryEvent, deliveredEvent));
        return JSON.toJSONString(routeDTO);
    }

    String routeJSON = "{\n" +
            "    \"Body\": {\n" +
            "        \"WaybillRoute\": [\n" +
            "            {\n" +
            "                \"mailno\": \"SF7444400031887\",\n" +
            "                \"acceptAddress\": \"深圳市\",\n" +
            "                \"reasonName\": \"\",\n" +
            "                \"orderid\": \"202003225d33322239ddW1df5t3\",\n" +
            "                \"acceptTime\": \"2020-05-11 16:56:54\",\n" +
            "                \"remark\": \"顺丰速运 已收取快件\",\n" +
            "                \"opCode\": \"50\",\n" +
            "                \"id\": \"158918741444476\",\n" +
            "                \"reasonCode\": \"\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"mailno\": \"SF7444400031887\",\n" +
            "                \"acceptAddress\": \"郑州市\",\n" +
            "                \"reasonName\": \"\",\n" +
            "                \"orderid\": \"202003225d33322239ddW1df5t3\",\n" +
            "                \"acceptTime\": \"2020-05-11 16:56:54\",\n" +
            "                \"remark\": \"快件到达 【郑州园博中转场】\",\n" +
            "                \"opCode\": \"31\",\n" +
            "                \"id\": \"158918741457126\",\n" +
            "                \"reasonCode\": \"\"\n" +
            "            }\n" +
            "        ]\n" +
            "    }\n" +
            "}";

}
