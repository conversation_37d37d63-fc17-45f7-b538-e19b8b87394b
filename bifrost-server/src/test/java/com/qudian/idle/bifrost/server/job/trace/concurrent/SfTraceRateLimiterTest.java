package com.qudian.idle.bifrost.server.job.trace.concurrent;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 顺丰API流控器测试
 * 
 * <AUTHOR>
 * @since 2025/09/02
 */
class SfTraceRateLimiterTest {
    
    private SfTraceRateLimiter rateLimiter;
    
    @BeforeEach
    void setUp() {
        rateLimiter = new SfTraceRateLimiter();
        rateLimiter.init();
    }
    
    @Test
    void testTryAcquire() {
        // 测试基本的获取许可功能
        assertTrue(rateLimiter.tryAcquire(), "应该能够获取许可");
    }
    
    @Test
    void testTryAcquireWithTimeout() {
        // 测试带超时的获取许可
        assertTrue(rateLimiter.tryAcquire(Duration.ofSeconds(1)), "应该能够在超时时间内获取许可");
    }
    
    @Test
    void testAcquire() {
        // 测试阻塞获取许可
        long startTime = System.currentTimeMillis();
        rateLimiter.acquire();
        long endTime = System.currentTimeMillis();
        
        // 第一次获取应该很快
        assertTrue(endTime - startTime < 100, "第一次获取许可应该很快");
    }
    
    @Test
    void testBatchAcquire() {
        // 测试批量获取许可
        int permits = 5;
        double waitTime = rateLimiter.acquire(permits);
        
        // 批量获取可能需要等待
        assertTrue(waitTime >= 0, "等待时间应该非负");
    }
    
    @Test
    @Timeout(10)
    void testRateLimit() {
        // 测试流控功能
        int attempts = 50; // 尝试获取50次
        int successCount = 0;
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < attempts; i++) {
            if (rateLimiter.tryAcquire()) {
                successCount++;
            }
        }
        
        long endTime = System.currentTimeMillis();
        double actualRate = successCount * 1000.0 / (endTime - startTime);
        
        System.out.println("尝试获取: " + attempts + ", 成功获取: " + successCount + 
                          ", 实际速率: " + String.format("%.1f", actualRate) + "/秒");
        
        // 实际速率应该接近配置的最大速率
        assertTrue(actualRate <= 35, "实际速率不应超过35/秒");
        assertTrue(successCount > 0, "应该能够获取到一些许可");
    }
    
    @Test
    @Timeout(15)
    void testConcurrentAccess() throws InterruptedException {
        int threadCount = 20;
        int callsPerThread = 3;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < callsPerThread; j++) {
                        if (rateLimiter.tryAcquire(Duration.ofMillis(500))) {
                            successCount.incrementAndGet();
                        } else {
                            failureCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        assertTrue(latch.await(12, TimeUnit.SECONDS), "所有线程应该在12秒内完成");
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        double duration = (endTime - startTime) / 1000.0;
        double actualRate = successCount.get() / duration;
        
        int totalCalls = successCount.get() + failureCount.get();
        assertEquals(threadCount * callsPerThread, totalCalls, "总调用次数应该正确");
        
        System.out.println("并发测试结果:");
        System.out.println("成功调用: " + successCount.get());
        System.out.println("失败调用: " + failureCount.get());
        System.out.println("实际速率: " + String.format("%.1f", actualRate) + "/秒");
        System.out.println("成功率: " + String.format("%.1f", successCount.get() * 100.0 / totalCalls) + "%");
        
        // 验证至少有一些调用成功
        assertTrue(successCount.get() > 0, "应该有一些调用成功");
        
        // 实际速率应该接近最大速率
        assertTrue(actualRate <= 35, "实际速率不应超过35/秒");
    }
    
    @Test
    void testDynamicRateAdjustment() {
        // 测试动态调整速率
        double originalRate = rateLimiter.getRate();
        double newRate = 50.0;
        
        rateLimiter.setRate(newRate);
        assertEquals(newRate, rateLimiter.getRate(), 0.1, "速率应该已更新");
        
        // 恢复原始速率
        rateLimiter.setRate(originalRate);
        assertEquals(originalRate, rateLimiter.getRate(), 0.1, "速率应该已恢复");
    }
    
    @Test
    void testWarmUp() {
        // 测试预热功能
        Duration warmupDuration = Duration.ofSeconds(1);
        
        long startTime = System.currentTimeMillis();
        rateLimiter.warmUp(warmupDuration);
        long endTime = System.currentTimeMillis();
        
        // 预热应该花费大约指定的时间
        long actualDuration = endTime - startTime;
        assertTrue(actualDuration >= warmupDuration.toMillis() * 0.8, 
                  "预热时间应该接近指定时间");
        assertTrue(actualDuration <= warmupDuration.toMillis() * 1.5, 
                  "预热时间不应该过长");
    }
    
    @Test
    void testGetters() {
        // 测试getter方法
        assertTrue(rateLimiter.getRate() > 0, "速率应该大于0");
        assertEquals(30.0, rateLimiter.getRate(), 0.1, "默认速率应该是30");
    }
}
