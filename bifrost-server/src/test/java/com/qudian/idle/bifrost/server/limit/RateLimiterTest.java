package com.qudian.idle.bifrost.server.limit;

import com.qudian.idle.bifrost.common.limiter.RedisLimiter;
import com.qudian.idle.bifrost.server.StartTestApplication;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * {@inheritDoc} 限流器测试类
 *
 * <AUTHOR>
 * @since 2023/8/16
 **/
public class RateLimiterTest extends StartTestApplication {

    private static final String KEY = "test_rateLimit";
    private static final String KEY2 = "test_rateLimit2";
    private static final String KEY3 = "test_rateLimit3";

    @Resource
    private RedisLimiter redisLimiter;

    @Test
    public void test() throws InterruptedException {
        // 1s 1次
        boolean res1 = redisLimiter.isOverLimit(KEY, 1, 1);
        boolean res2 = redisLimiter.isOverLimit(KEY, 1, 1);
        boolean res3 = redisLimiter.isOverLimit(KEY, 1, 1);
        TimeUnit.SECONDS.sleep(1);
        boolean res4 = redisLimiter.isOverLimit(KEY, 1, 1);
        Assert.assertFalse(res1);
        Assert.assertTrue(res2);
        Assert.assertTrue(res3);
        Assert.assertFalse(res4);

        // 1s/2次
        boolean res5 = redisLimiter.isOverLimit(KEY2, 1, 2);
        boolean res6 = redisLimiter.isOverLimit(KEY2, 1, 2);
        boolean res7 = redisLimiter.isOverLimit(KEY2, 1, 2);
        boolean res8 = redisLimiter.isOverLimit(KEY2, 1, 2);
        TimeUnit.SECONDS.sleep(1);
        boolean res9 = redisLimiter.isOverLimit(KEY, 1, 2);
        boolean res10 = redisLimiter.isOverLimit(KEY, 1, 2);

        Assert.assertFalse(res5);
        Assert.assertFalse(res6);
        Assert.assertTrue(res7);
        Assert.assertTrue(res8);
        Assert.assertFalse(res9);
        Assert.assertFalse(res10);

        // 2s/1次
        boolean res11 = redisLimiter.isOverLimit(KEY3, 2, 1);
        boolean res12 = redisLimiter.isOverLimit(KEY3, 2, 1);
        TimeUnit.SECONDS.sleep(1);
        boolean res13 = redisLimiter.isOverLimit(KEY3, 2, 1);
        TimeUnit.SECONDS.sleep(1);
        boolean res14 = redisLimiter.isOverLimit(KEY3, 2, 1);

        Assert.assertFalse(res11);
        Assert.assertTrue(res12);
        Assert.assertTrue(res13);
        Assert.assertFalse(res14);
    }
}
