package com.qudian.idle.bifrost.server.facade;

import com.qudian.idle.bifrost.api.facade.express.ExpressFacade;
import com.qudian.idle.bifrost.api.vo.request.express.trace.SearchSingleRouteReqVO;
import com.qudian.idle.bifrost.server.StartTestApplication;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

import javax.annotation.Resource;

/**
 * <p>文件名称:com.qudian.idle.bifrost.server.facade.ExpressFacadeTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/9/1
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ExpressFacadeTest extends StartTestApplication {
    @Resource
    private ExpressFacade expressFacade;

    @Test
    public void test01_querySingleRoute() {
        SearchSingleRouteReqVO reqVO = new SearchSingleRouteReqVO();
        reqVO.setWaybillNo("SF7444498576611");
        expressFacade.querySingleRoute(reqVO);
    }

}
