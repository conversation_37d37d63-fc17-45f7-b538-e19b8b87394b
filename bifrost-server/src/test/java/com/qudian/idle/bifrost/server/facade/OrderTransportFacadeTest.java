package com.qudian.idle.bifrost.server.facade;

import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSON;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.idle.bifrost.api.facade.OrderTransportFacade;
import com.qudian.idle.bifrost.api.vo.request.OrderTransportReqVO;
import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.idle.bifrost.infrastructure.annotation.DubboReference;
import com.qudian.idle.bifrost.server.StartTestApplication;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

import java.util.Random;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class OrderTransportFacadeTest extends StartTestApplication {

    @DubboReference
    private OrderTransportFacade orderTransportFacade;

    private static final Random RANDOM = new Random(System.currentTimeMillis());

    @Test
    public void pushTransport() {
       /* String s = "[{\"orderNumber\":\"test-%s\",\"orderType\":\"\",\"forwarderType\":\"ewe\",\"senderInformation\":{\"sender\":\"Sydney\",\"senderMobilePhone\":\"0296442648\",\"senderAddress\":\"6 Chifley Driveurterdfhjgsdal;fkvmkghkfsdojermhasuiffbmghljsldfgdgnfkghsdm,lkgdsdmvdlkgdfkjsdfdsfgjfkgjs\",\"senderCountry\":\"AU\",\"senderProvince\":\"VIC\",\"senderCity\":\"Moorabbin Airport\",\"senderPostcode\":\"3194\"},\"recipientInformation\":{\"to\":\"TEST\",\"recipientMobilePhone\":\"0404280016\",\"recipientAddress\":\"6 Chifley Drive\",\"receiptCountry\":\"AU\",\"receiptProvince\":\"NSW\",\"receiptCity\":\"Croydon\",\"receiptPostcode\":\"2132\"},\"itemInformation\":[{\"weight\":4.0,\"width\":5.0,\"length\":5.0,\"height\":5.0,\"itemDescription\":\"4545454\"}]}]";

        String json = String.format(s, RANDOM.nextInt());
        List<PushTransportReqVO> reqVO = JSON.parseObject(json, new TypeReference<List<PushTransportReqVO>>() {
        });

        BaseResponseDTO<List<PushTransportResponseVO>> listBaseResponseDTO = orderTransportFacade.pushTransport(reqVO);
        log.warn(JSON.toJSONString(listBaseResponseDTO));*/
    }

    @Test
    public void test_pushSingle() {
        PushSingleTransportReqVO reqVO = new PushSingleTransportReqVO();
        reqVO.setChannel("ewe");
        reqVO.setShipment(PushSingleTransportReqVO.Shipment.builder().orderNumber("123123123").build());

        BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransportResponseVOBaseResponseDTO = orderTransportFacade.pushSingleTransport(reqVO);
        log.warn("---" + JSON.toJSONString(pushSingleTransportResponseVOBaseResponseDTO));
    }

//    public void printLabelV2() {
//        OrderTransportReqVO orderTransportReqVO = new OrderTransportReqVO();
//        orderTransportReqVO.setOrderNumbers(Lists.newArrayList("test0807-01"));
//
//        BaseResponseDTO<PrintLabelResponseVO> stringBaseResponseDTO = orderTransportFacade.printLabelV2(orderTransportReqVO);
//        log.warn(JSON.toJSONString(stringBaseResponseDTO));
//    }

    @Test
    public void test2() throws NoSuchAlgorithmException, InvalidKeyException {
        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT")); // 设置时区为GMT
        String str = sdf.format(cd.getTime());
        System.out.println(str);
        String s= "POST"+"\n"+str+"\n"+"https://wisecomm.wiseway.com.au/services/shipper/orderLabels";
        String sign = encrypt(s, "cxfk17xo7aKSS5722lDM_w");
        System.out.println(sign);
    }

    private static String encrypt(String data, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] result = null;
        SecretKeySpec signinKey = new SecretKeySpec(secret.getBytes(), HmacAlgorithm.HmacSHA1.getValue());
        //生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(HmacAlgorithm.HmacSHA1.getValue());
        //用给定密钥初始化 Mac 对象
        mac.init(signinKey);
        //完成 Mac 操作
        byte[] rawHmac = mac.doFinal(data.getBytes());
        result = Base64.getEncoder().encode(rawHmac);

        return new String(result);

    }


}
