//package com.qudian.idle.bifrost.server.facade;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.qudian.java.components.common.dto.BaseResponseDTO;
//import com.qudian.idle.bifrost.api.enums.CarrierTypeEnum;
//import com.qudian.idle.bifrost.api.facade.OrderTransportFacade;
//import com.qudian.idle.bifrost.api.facade.TransportTrackFacade;
//import com.qudian.idle.bifrost.api.vo.request.CancelTransportReqVO;
//import com.qudian.idle.bifrost.api.vo.request.PushSingleTransportReqVO;
//import com.qudian.idle.bifrost.api.vo.request.SingleOrderTransportReqVO;
//import com.qudian.idle.bifrost.api.vo.request.TransportTrackReqVO;
//import com.qudian.idle.bifrost.api.vo.response.PrintLabelResponseVO;
//import com.qudian.idle.bifrost.api.vo.response.PushSingleTransportResponseVO;
//import com.qudian.idle.bifrost.api.vo.response.TransportTrackRespVO;
//import com.qudian.idle.bifrost.application.service.OrderTransportService;
//import com.qudian.idle.bifrost.common.utils.common.Utils;
//import com.qudian.idle.bifrost.infrastructure.annotation.DubboReference;
//import com.qudian.idle.bifrost.infrastructure.remote.SendSpeedRemoteService;
//import com.qudian.idle.bifrost.infrastructure.remote.WiseWayService;
//import com.qudian.idle.bifrost.infrastructure.repository.mapper.OrderTransportMapper;
//import com.qudian.idle.bifrost.infrastructure.repository.po.OrderTransportPO;
//import com.qudian.idle.bifrost.server.StartTestApplication;
//import com.qudian.pay.settle.api.dto.ChannelInfoReqDTO;
//import com.qudian.pay.settle.api.dto.ChannelInfoRespDTO;
//import com.qudian.pay.settle.api.dto.SettleRespDTO;
//import com.qudian.pay.settle.api.service.ChannelInfoFacede;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.platform.commons.util.CollectionUtils;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.annotation.Rollback;
//
//import javax.annotation.Resource;
//import java.util.*;
//
//import static com.qudian.idle.bifrost.api.enums.CarrierTypeEnum.AUS_POST_WISE_WAY;
//
//
///**
// * {@inheritDoc} {@link TransportTrackFacade} test
// *
// * <AUTHOR>
// * @since 2023/8/1
// **/
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
//@Slf4j
//public class TransportTrackFacadeTest extends StartTestApplication {
//
//    @DubboReference
//    private TransportTrackFacade transportTrackFacade;
//    @Resource
//    private OrderTransportMapper orderTransportMapper;
//
//    @Autowired
//    private SendSpeedRemoteService sendSpeedRemoteService;
//
//    private static final String ORDER_NUMBER = "F00000000176186";
//    private static final String FAKE_ORDER_NUMBER = "F1236799923";
//
//    @Autowired
//    private WiseWayService wiseWayService;
//    @Autowired
//    private OrderTransportService orderTransportService;
//
//    @Resource
//    private OrderTransportFacade orderTransportFacade;
//    @Reference(version = "1.0.1", check = false, timeout = 12000, url = "35.247.172.247:20840")
//    private ChannelInfoFacede channelInfoFacede;
//
//    @Test
//    public void test() {
//        TransportTrackReqVO reqVO = new TransportTrackReqVO();
//        reqVO.setOrderNumberList(Lists.newArrayList(ORDER_NUMBER, FAKE_ORDER_NUMBER));
//        prepare();
//
//        BaseResponseDTO<TransportTrackRespVO> dto = transportTrackFacade.queryTransportTrack(reqVO);
//        log.warn(JSON.toJSONString(dto));
//    }
//
//    @Test
//    public void test_track_usps() {
//        TransportTrackReqVO reqVO = new TransportTrackReqVO();
//        reqVO.setOrderNumberList(Lists.newArrayList("test0911-7272722162"));
//        prepare();
//
//        BaseResponseDTO<TransportTrackRespVO> dto = transportTrackFacade.queryTransportTrack(reqVO);
//        log.warn(JSON.toJSONString(dto));
//    }
//
//    private void prepare() {
//        OrderTransportPO ewe = OrderTransportPO.builder()
//            .id(9999911L)
//            .createdTime(new Date())
//            .updatedTime(new Date())
//            .deleteFlag(0)
//            .orderNumber(ORDER_NUMBER)
//            .forwarderType("ewe")
//            .carrier(CarrierTypeEnum.AUS_POST.getName())
//            .status(1)
//            .forwarderStatus("1")
//            .articleId("2123")
//            .build();
//        OrderTransportPO fake = OrderTransportPO.builder()
//            .id(9999912L)
//            .createdTime(new Date())
//            .updatedTime(new Date())
//            .deleteFlag(0)
//            .orderNumber(FAKE_ORDER_NUMBER)
//            .forwarderType("ewe")
//            .carrier(CarrierTypeEnum.AUS_POST.getName())
//            .status(1)
//            .forwarderStatus("1")
//            .articleId("2124")
//            .build();
//
//        orderTransportMapper.(Lists.newArrayList(ewe, fake));
//    }
//
//    @Test
//    @Rollback(value = false)
//    public void testaaa(){
//       String s = "{\n" +
//               "  \"carrier\":\"\",\n" +
//               "  \"channel\":\"cainiao\",\n" +
//               "  \"forwarderType\":\"ewe\",\n" +
//               "  \"shipment\":{\n" +
//               "    \"itemInfo\":[\n" +
//               "      {\n" +
//               "        \"height\":20.0,\n" +
//               "        \"itemDescription\":\"合同\",\n" +
//               "        \"length\":40.0,\n" +
//               "        \"weight\":0.9,\n" +
//               "        \"width\":20.0\n" +
//               "      }\n" +
//               "    ],\n" +
//               "    \"orderNumber\":\"AUL1705737409010023232\",\n" +
//               "    \"recipientInfo\":{\n" +
//               "      \"county\":\"Keysborough\",\n" +
//               "      \"email\":\"<EMAIL>\",\n" +
//               "      \"receiptCity\":\"\",\n" +
//               "      \"receiptCountry\":\"AU\",\n" +
//               "      \"receiptPostcode\":\"3173\",\n" +
//               "      \"receiptProvince\":\"VIC\",\n" +
//               "      \"recipient\":\"fortest\",\n" +
//               "      \"recipientAddress\":\"200 Cheltenham Rd\",\n" +
//               "      \"recipientMobilePhone\":\"61485825837\"\n" +
//               "    },\n" +
//               "    \"returneeInfo\":{\n" +
//               "      \"county\":\"Truganina\",\n" +
//               "      \"returnee\":\"liuyunpeng-1\",\n" +
//               "      \"returneeAddress\":\"10 Drinkwater St, Truganina VIC 3029, AU\",\n" +
//               "      \"returneeCity\":\"\",\n" +
//               "      \"returneeCountry\":\"au\",\n" +
//               "      \"returneeMobilePhone\":\"\",\n" +
//               "      \"returneePostcode\":\"3029\",\n" +
//               "      \"returneeProvince\":\"VIC\"\n" +
//               "    },\n" +
//               "    \"senderInfo\":{\n" +
//               "      \"county\":\"Fairfield\",\n" +
//               "      \"sender\":\"yml-temu\",\n" +
//               "      \"senderAddress\":\"528A The Horsley Dr\",\n" +
//               "      \"senderCity\":\"\",\n" +
//               "      \"senderCountry\":\"AU\",\n" +
//               "      \"senderMobilePhone\":\"61466059079\",\n" +
//               "      \"senderPostcode\":\"2165\",\n" +
//               "      \"senderProvince\":\"NSW\"\n" +
//               "    }\n" +
//               "  },\n" +
//               "  \"warehouseLocationState\":\"VIC\"\n" +
//               "}";
//        // 将json 转换成对象
//        PushSingleTransportReqVO pushSingleTransportReqVO = JSON.parseObject(s, PushSingleTransportReqVO.class);
//        pushSingleTransportReqVO.setChannel("TENIU");
//        pushSingleTransportReqVO.setOrigin("TENIU");
//        BaseResponseDTO<PushSingleTransportResponseVO> order = orderTransportFacade.pushSingleTransport(pushSingleTransportReqVO);
//        System.out.println(order.toString());
//    }
//    @Test
//    @Rollback(value = false)
//    public void testCancel(){
//        CancelTransportReqVO cancelTransportReqVO = new CancelTransportReqVO();
//        cancelTransportReqVO.setOrderNumber("test2609-123459");
//        cancelTransportReqVO.setReason("aaaa");
//        orderTransportFacade.cancelTransport(cancelTransportReqVO);
//    }
//    @Test
//    public void testPrintLabel(){
//        SingleOrderTransportReqVO singleOrderTransportReqVO = new SingleOrderTransportReqVO();
//        singleOrderTransportReqVO.setOrderNumber("EX17084966221001");
//        BaseResponseDTO<PrintLabelResponseVO> printLabelResponseVOBaseResponseDTO = orderTransportFacade.printSingleLabel(singleOrderTransportReqVO);
//        System.out.println(printLabelResponseVOBaseResponseDTO);
//    }
//
////    @Test
////    public void testbbb() throws Exception {
////        String s = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
////                "<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ns1=\"http://www.example.org/Ec/\">\n" +
////                "\t<SOAP-ENV:Body>\n" +
////                "\t\t<ns1:callServiceResponse>\t\t\t\n" +
////                "\t\t<response><![CDATA[{\"ask\":\"Failure\",\"message\":\"鉴权失败\",\"err_code\":\"50002\"}]]></response>\n" +
////                "\t\t</ns1:callServiceResponse>\n" +
////                "\t</SOAP-ENV:Body>\n" +
////                "</SOAP-ENV:Envelope>";
////        String callServiceResponse = deserializeSoapResponse(s);
////        System.out.println(callServiceResponse);
////    }
//
//    @Test
//    @Rollback(value = false)
//    public void test222(){
////        String s = "{\n" +
////                "        \"orderNumber\": \"test2609-123459\",\n" +
////                "        \"orderType\": \"\",\n" +
////                "        \"forwarderType\": \"\",\n" +
////                "        \"returneeInfo\": {\n" +
////                "            \"sender\": \"CAN01\",\n" +
////                "            \"senderMobilePhone\": \"0296442648\",\n" +
////                "            \"senderAddress\": \"199 Konggang 6 Rd\",\n" +
////                "            \"senderCountry\": \"CN\",\n" +
////                "            \"senderProvince\": \"ShangHai\",\n" +
////                "            \"senderCity\": \"ShangHai\",\n" +
////                "            \"senderPostcode\": \"200000\"\n" +
////                "        },\n" +
////                "        \"recipientInfo\": {\n" +
////                "            \"recipient\": \"TEST\",\n" +
////                "            \"recipientMobilePhone\": \"0033972190012\",\n" +
////                "            \"recipientAddress\": \"401 6th St\",\n" +
////                "            \"receiptCountry\": \"AU\",\n" +
////                "            \"receiptProvince\": \"NSW\",\n" +
////                "            \"receiptCity\": \"Chipping Norton\",\n" +
////                "            \"receiptPostcode\": \"2170\"\n" +
////                "        },\n" +
////                "        \"itemInfo\": [\n" +
////                "            {\n" +
////                "                \"weight\": 1.0,\n" +
////                "                \"width\": 1.0,\n" +
////                "                \"length\": 1.0,\n" +
////                "                \"height\": 1.0,\n" +
////                "                \"itemDescription\": \"4545454\"\n" +
////                "            }\n" +
////                "        ]\n" +
////                "    }";
//        // 将json 转换成对象
//String s = "{\"carrier\":\"Australia Post\",\"channel\":\"\",\"forwarderType\":\"ewe\",\"shipment\":{\"itemInfo\":[{\"height\":0.0,\"itemDescription\":\"Fruit\",\"length\":0.0,\"weight\":0.0,\"width\":0.0}],\"orderNumber\":\"EX17083919041101\",\"recipientInfo\":{\"county\":\"\",\"receiptCity\":\"Sydney Olympic Park\",\"receiptCountry\":\"AU\",\"receiptPostcode\":\"2127\",\"receiptProvince\":\"NSW\",\"recipient\":\"wang\",\"recipientAddress\":\"\",\"recipientMobilePhone\":\"+61 *********\"},\"returneeInfo\":{\"county\":\"Truganina\",\"returnee\":\"王智平\",\"returneeAddress\":\"38 Clark St, Williams Landing VIC 3027, AU\",\"returneeCity\":\"\",\"returneeCountry\":\"AU\",\"returneeMobilePhone\":\"\",\"returneePostcode\":\"3026\",\"returneeProvince\":\"VIC\"},\"senderInfo\":{\"county\":\"Truganina\",\"sender\":\"王智平\",\"senderAddress\":\"38 Clark St, Williams Landing VIC 3027, AU\",\"senderCity\":\"\",\"senderCountry\":\"AU\",\"senderMobilePhone\":\"\",\"senderPostcode\":\"3026\",\"senderProvince\":\"VIC\"}},\"warehouseLocationState\":\"VIC\"}";
////        PushSingleTransportReqVO.Shipment pushSingleTransportReqVO = JSON.parseObject(s, PushSingleTransportReqVO.Shipment.class);
////        pushSingleTransportReqVO.setItemInfo(new ArrayList<>());
////        PushSingleTransportReqVO build = PushSingleTransportReqVO.builder().build();
////        build.setShipment(pushSingleTransportReqVO);
////        build.setCarrier(AUS_POST_WISE_WAY.getName());
////        build.setChannel("TENIU");
////        build.setWarehouseLocationState("VIC");
//        PushSingleTransportReqVO build =  JSON.parseObject(s,PushSingleTransportReqVO.class);
//        BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransportResponseVOBaseResponseDTO = orderTransportService.pushSingleTransport(build);
//        System.out.println(pushSingleTransportResponseVOBaseResponseDTO);
//    }
//
//    @Test
//    @Rollback(value = false)
//    public void test2222(){
//        CancelTransportReqVO cancelTransportReqVO = new CancelTransportReqVO();
//        cancelTransportReqVO.setCarrier(AUS_POST_WISE_WAY.getName());
//        cancelTransportReqVO.setOrderNumber("test2609-12345222");
//        BaseResponseDTO<String> stringBaseResponseDTO = orderTransportService.cancelTransport(cancelTransportReqVO);
//        System.out.println(stringBaseResponseDTO);
//    }
//
//    @Test
//    @Rollback(value = false)
//    public void test3333(){
//        SingleOrderTransportReqVO cancelTransportReqVO = new SingleOrderTransportReqVO();
//        cancelTransportReqVO.setCarrier(AUS_POST_WISE_WAY.getName());
//        cancelTransportReqVO.setOrderNumber("test2609-12345222");
//        PrintLabelResponseVO printLabelResponseVO = orderTransportService.printSingleLabel(cancelTransportReqVO);
//        System.out.println(printLabelResponseVO);
//    }
//
//    @Test
//    @Rollback(value = false)
//    public void test444(){
//        SingleOrderTransportReqVO cancelTransportReqVO = new SingleOrderTransportReqVO();
////        cancelTransportReqVO.setCarrier(AUS_POST_WISE_WAY.getName());
//        cancelTransportReqVO.setOrderNumber("EX17090160531101");
//        BaseResponseDTO<String> stringBaseResponseDTO = orderTransportService.dispatchSingleTransport(cancelTransportReqVO);
//        System.out.println(stringBaseResponseDTO);
//    }
//
//    @Test
//    public void te23st(){
//        SettleRespDTO<List<ChannelInfoRespDTO>> cainiaow1 = channelInfoFacede.listChannelInfo(ChannelInfoReqDTO.builder().channelName("cainiaow").build());
//        System.out.println(JSON.toJSONString(cainiaow1));
//    }
////    @Test
////    public void aaa(){
////        System.out.println(Utils.ignoreExpression("😊aaaa"));;
////    }
//
//
//
//
//}
